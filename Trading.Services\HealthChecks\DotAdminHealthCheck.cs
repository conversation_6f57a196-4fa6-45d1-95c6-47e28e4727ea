using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces.DotAdmin;

namespace Trading.Services.HealthChecks
{
  /// <summary>
  /// Health check for dotAdmin API connectivity and authentication
  /// </summary>
  public class DotAdminHealthCheck : IHealthCheck
  {
    private readonly IDotAdminService _dotAdminService;
    private readonly DotAdminDTO _config;
    private readonly ILogger<DotAdminHealthCheck> _logger;

    public DotAdminHealthCheck(
      IDotAdminService dotAdminService,
      IOptionsSnapshot<DotAdminDTO> config,
      ILogger<DotAdminHealthCheck> logger)
    {
      _dotAdminService = dotAdminService ?? throw new ArgumentNullException(nameof(dotAdminService));
      _config = config.Value ?? throw new ArgumentNullException(nameof(config));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
      HealthCheckContext context, 
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogDebug("Starting dotAdmin health check");

        // Check configuration
        var configResult = CheckConfiguration();
        if (configResult != null)
        {
          return configResult;
        }

        // Check authentication
        var authResult = await CheckAuthenticationAsync(cancellationToken);
        if (authResult != null)
        {
          return authResult;
        }

        _logger.LogDebug("dotAdmin health check passed");
        return HealthCheckResult.Healthy("dotAdmin API is accessible and authentication is working");
      }
      catch (OperationCanceledException)
      {
        _logger.LogWarning("dotAdmin health check was cancelled");
        return HealthCheckResult.Unhealthy("dotAdmin health check was cancelled");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "dotAdmin health check failed with unexpected error");
        return HealthCheckResult.Unhealthy($"dotAdmin health check failed: {ex.Message}", ex);
      }
    }

    private HealthCheckResult? CheckConfiguration()
    {
      var issues = new List<string>();

      if (string.IsNullOrWhiteSpace(_config.BaseUrl))
        issues.Add("BaseUrl is not configured");

      if (string.IsNullOrWhiteSpace(_config.Username))
        issues.Add("Username is not configured");

      if (string.IsNullOrWhiteSpace(_config.Password))
        issues.Add("Password is not configured");

      if (_config.DefaultCustomerId <= 0)
        issues.Add("DefaultCustomerId is not configured or invalid");

      if (_config.DefaultLocationId <= 0)
        issues.Add("DefaultLocationId is not configured or invalid");

      if (_config.TimeoutSeconds <= 0)
        issues.Add("TimeoutSeconds is not configured or invalid");

      if (issues.Any())
      {
        var message = $"dotAdmin configuration issues: {string.Join(", ", issues)}";
        _logger.LogWarning(message);
        return HealthCheckResult.Unhealthy(message);
      }

      return null;
    }

    private async Task<HealthCheckResult?> CheckAuthenticationAsync(CancellationToken cancellationToken)
    {
      try
      {
        // Check if already authenticated
        if (_dotAdminService.IsAuthenticated)
        {
          _logger.LogDebug("dotAdmin service is already authenticated");
          return null;
        }

        // Try to authenticate
        _logger.LogDebug("Attempting dotAdmin authentication");
        var authSuccess = await _dotAdminService.AuthenticateAsync(cancellationToken);

        if (!authSuccess)
        {
          var message = "dotAdmin authentication failed";
          _logger.LogWarning(message);
          return HealthCheckResult.Degraded(message);
        }

        _logger.LogDebug("dotAdmin authentication successful");
        return null;
      }
      catch (TaskCanceledException)
      {
        var message = "dotAdmin authentication timed out";
        _logger.LogWarning(message);
        return HealthCheckResult.Degraded(message);
      }
      catch (Exception ex)
      {
        var message = $"dotAdmin authentication error: {ex.Message}";
        _logger.LogError(ex, message);
        return HealthCheckResult.Unhealthy(message, ex);
      }
    }
  }

  /// <summary>
  /// Extension methods for registering dotAdmin health checks
  /// </summary>
  public static class DotAdminHealthCheckExtensions
  {
    /// <summary>
    /// Add dotAdmin health check to the health check builder
    /// </summary>
    public static IHealthChecksBuilder AddDotAdminHealthCheck(
      this IHealthChecksBuilder builder,
      string name = "dotadmin",
      HealthStatus? failureStatus = null,
      IEnumerable<string>? tags = null,
      TimeSpan? timeout = null)
    {
      return builder.AddCheck<DotAdminHealthCheck>(
        name,
        failureStatus ?? HealthStatus.Unhealthy,
        tags,
        timeout);
    }
  }
}
