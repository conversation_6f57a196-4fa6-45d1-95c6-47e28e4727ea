using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.InspectCollect.Controllers
{
  /// <summary>
  /// Controller for dotAdmin auction operations
  /// Provides endpoints for pushing vehicles to dotAdmin auction platform
  /// </summary>
  [ApiController]
  [Route("api/[controller]")]
  [Produces("application/json")]
  public class DotAdminController : ControllerBase
  {
    private readonly IDotAdminService _dotAdminService;
    private readonly ILogger<DotAdminController> _logger;

    public DotAdminController(IDotAdminService dotAdminService, ILogger<DotAdminController> logger)
    {
      _dotAdminService = dotAdminService ?? throw new ArgumentNullException(nameof(dotAdminService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Create a vehicle in dotAdmin auction from ICVehicle data
    /// </summary>
    /// <param name="request">Vehicle creation request containing ICVehicle ID and location information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information from dotAdmin</returns>
    /// <response code="200">Vehicle successfully created in dotAdmin</response>
    /// <response code="400">Invalid request parameters or ICVehicle data</response>
    /// <response code="500">Internal server error during vehicle creation</response>
    [HttpPost("vehicles/from-ic-vehicle")]
    [ProducesResponseType(typeof(DotAdminVehicle), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [ProducesResponseType(typeof(string), 500)]
    public async Task<ActionResult<DotAdminVehicle>> CreateVehicleFromICVehicle(
      [FromBody] CreateVehicleFromICVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Creating dotAdmin vehicle from ICVehicle {ICVehicleId}", request.ICVehicleId);

        var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
          request.ICResponseId,
          request.ICVehicleId,
          request.LocationId,
          request.CustomerId,
          cancellationToken);

        return Ok(vehicle);
      }
      catch (ArgumentException ex)
      {
        _logger.LogWarning(ex, "Invalid request parameters");
        return BadRequest(ex.Message);
      }
      catch (InvalidOperationException ex)
      {
        _logger.LogWarning(ex, "Invalid operation");
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating vehicle from ICVehicle");
        return StatusCode(500, "An error occurred while creating the vehicle");
      }
    }

    /// <summary>
    /// Create a vehicle in dotAdmin auction with basic registration and VIN information
    /// </summary>
    /// <param name="request">Basic vehicle creation request with registration, VIN, customer and location</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information from dotAdmin</returns>
    /// <response code="200">Vehicle successfully created in dotAdmin</response>
    /// <response code="400">Invalid request parameters (missing registration, invalid IDs, etc.)</response>
    /// <response code="500">Internal server error during vehicle creation</response>
    /// <remarks>
    /// This endpoint creates a vehicle using basic information. If UseLookup is true (default),
    /// dotAdmin will automatically populate additional vehicle details using their lookup service.
    /// 
    /// Sample request:
    /// 
    ///     POST /api/dotadmin/vehicles
    ///     {
    ///         "registration": "AB12 CDE",
    ///         "vin": "1234567890",
    ///         "customerId": 3787,
    ///         "locationId": 12,
    ///         "useLookup": true
    ///     }
    /// 
    /// </remarks>
    [HttpPost("vehicles")]
    [ProducesResponseType(typeof(DotAdminVehicle), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [ProducesResponseType(typeof(string), 500)]
    public async Task<ActionResult<DotAdminVehicle>> CreateVehicle(
      [FromBody] CreateVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Creating dotAdmin vehicle with registration {Registration}", request.Registration);

        var vehicle = await _dotAdminService.CreateVehicleAsync(
          request.Registration,
          request.Vin,
          request.CustomerId,
          request.LocationId,
          request.UseLookup,
          cancellationToken);

        return Ok(vehicle);
      }
      catch (ArgumentException ex)
      {
        _logger.LogWarning(ex, "Invalid request parameters");
        return BadRequest(ex.Message);
      }
      catch (InvalidOperationException ex)
      {
        _logger.LogWarning(ex, "Invalid operation");
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating vehicle");
        return StatusCode(500, "An error occurred while creating the vehicle");
      }
    }

    /// <summary>
    /// Create a vehicle in dotAdmin auction with comprehensive vehicle details
    /// </summary>
    /// <param name="request">Detailed vehicle creation request with all available vehicle information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information from dotAdmin</returns>
    /// <response code="200">Vehicle successfully created in dotAdmin</response>
    /// <response code="400">Invalid request parameters or vehicle data</response>
    /// <response code="500">Internal server error during vehicle creation</response>
    /// <remarks>
    /// This endpoint allows creating a vehicle with comprehensive details. Use this when you have
    /// detailed vehicle information and want to minimize reliance on dotAdmin's lookup service.
    /// </remarks>
    [HttpPost("vehicles/detailed")]
    [ProducesResponseType(typeof(DotAdminVehicle), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [ProducesResponseType(typeof(string), 500)]
    public async Task<ActionResult<DotAdminVehicle>> CreateVehicleDetailed(
      [FromBody] DotAdminCreateVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Creating dotAdmin vehicle with detailed request");

        var vehicle = await _dotAdminService.CreateVehicleWithDetailsAsync(request, cancellationToken);

        return Ok(vehicle);
      }
      catch (ArgumentException ex)
      {
        _logger.LogWarning(ex, "Invalid request parameters");
        return BadRequest(ex.Message);
      }
      catch (InvalidOperationException ex)
      {
        _logger.LogWarning(ex, "Invalid operation");
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating detailed vehicle");
        return StatusCode(500, "An error occurred while creating the vehicle");
      }
    }

    /// <summary>
    /// Get current authentication status and session information
    /// </summary>
    /// <returns>Authentication status including current customer and location</returns>
    /// <response code="200">Successfully retrieved authentication status</response>
    /// <remarks>
    /// Returns the current authentication status, including whether the service is authenticated
    /// and the currently selected customer and location IDs.
    /// </remarks>
    [HttpGet("auth-status")]
    [ProducesResponseType(typeof(AuthStatusResponse), 200)]
    public ActionResult<AuthStatusResponse> GetAuthStatus()
    {
      return Ok(new AuthStatusResponse
      {
        IsAuthenticated = _dotAdminService.IsAuthenticated,
        CurrentCustomerId = _dotAdminService.CurrentCustomerId,
        CurrentLocationId = _dotAdminService.CurrentLocationId
      });
    }

    /// <summary>
    /// Manually trigger authentication with dotAdmin using configured credentials
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication success status</returns>
    /// <response code="200">Authentication attempt completed (check response value for success)</response>
    /// <response code="500">Internal server error during authentication</response>
    /// <remarks>
    /// This endpoint manually triggers authentication with dotAdmin using the configured credentials.
    /// Authentication is typically handled automatically when needed, but this endpoint can be used
    /// to explicitly authenticate or re-authenticate the session.
    /// </remarks>
    [HttpPost("authenticate")]
    [ProducesResponseType(typeof(bool), 200)]
    [ProducesResponseType(typeof(string), 500)]
    public async Task<ActionResult<bool>> Authenticate(CancellationToken cancellationToken = default)
    {
      try
      {
        var success = await _dotAdminService.AuthenticateAsync(cancellationToken);
        return Ok(success);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error during authentication");
        return StatusCode(500, "An error occurred during authentication");
      }
    }
  }

  // Request/Response DTOs for the controller
  public class CreateVehicleFromICVehicleRequest
  {
    [Required]
    public Guid ICResponseId { get; set; }

    [Required]
    public Guid ICVehicleId { get; set; }

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "LocationId must be a positive integer")]
    public int LocationId { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "CustomerId must be a positive integer")]
    public int? CustomerId { get; set; }
  }

  public class CreateVehicleRequest
  {
    [Required]
    [StringLength(20, MinimumLength = 2, ErrorMessage = "Registration must be between 2 and 20 characters")]
    public string Registration { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "VIN cannot exceed 50 characters")]
    public string? Vin { get; set; }

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "CustomerId must be a positive integer")]
    public int CustomerId { get; set; }

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "LocationId must be a positive integer")]
    public int LocationId { get; set; }

    public bool UseLookup { get; set; } = true;
  }

  public class AuthStatusResponse
  {
    public bool IsAuthenticated { get; set; }
    public int? CurrentCustomerId { get; set; }
    public int? CurrentLocationId { get; set; }
  }
}
