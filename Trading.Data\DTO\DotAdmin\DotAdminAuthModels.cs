using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Trading.API.Data.DTO.DotAdmin
{
  /// <summary>
  /// Request model for dotAdmin authentication
  /// </summary>
  public class DotAdminAuthRequest
  {
    [JsonPropertyName("username")]
    public string Username { get; set; }

    [JsonPropertyName("password")]
    public string Password { get; set; }

    [JsonPropertyName("customer_id")]
    public int? CustomerId { get; set; }

    [JsonPropertyName("location_id")]
    public int? LocationId { get; set; }
  }

  /// <summary>
  /// Response model for dotAdmin authentication
  /// </summary>
  public class DotAdminAuthResponse
  {
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("token")]
    public string Token { get; set; }

    [JsonPropertyName("iat")]
    public long IssuedAt { get; set; }

    [JsonPropertyName("exp")]
    public long ExpiresAt { get; set; }

    [JsonPropertyName("next")]
    public string Next { get; set; }

    [JsonPropertyName("customers")]
    public List<DotAdminCustomer> Customers { get; set; } = new List<DotAdminCustomer>();

    [JsonPropertyName("locations")]
    public Dictionary<string, string> Locations { get; set; } = new Dictionary<string, string>();

    /// <summary>
    /// Helper property to get expiration as DateTime
    /// </summary>
    public DateTime ExpirationDateTime => DateTimeOffset.FromUnixTimeSeconds(ExpiresAt).DateTime;

    /// <summary>
    /// Helper property to check if token is expired or will expire soon
    /// </summary>
    public bool IsExpiredOrExpiringSoon(int bufferSeconds = 300)
    {
      return DateTime.UtcNow.AddSeconds(bufferSeconds) >= ExpirationDateTime;
    }
  }

  /// <summary>
  /// Request model for selecting customer and location
  /// </summary>
  public class DotAdminCustomerLocationRequest
  {
    [JsonPropertyName("customer_id")]
    public int CustomerId { get; set; }

    [JsonPropertyName("location_id")]
    public int LocationId { get; set; }
  }

  /// <summary>
  /// Customer information from dotAdmin
  /// </summary>
  public class DotAdminCustomer
  {
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("email")]
    public string Email { get; set; }

    /// <summary>
    /// Helper property to get ID as integer
    /// </summary>
    public int IdAsInt => int.TryParse(Id, out var result) ? result : 0;
  }
}
