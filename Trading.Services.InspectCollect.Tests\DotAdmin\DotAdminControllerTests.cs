using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Trading.API.Data.DTO.DotAdmin;
using Trading.API.InspectCollect.Controllers;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.Services.InspectCollect.Tests.DotAdmin
{
  public class DotAdminControllerTests
  {
    private readonly Mock<IDotAdminService> _serviceMock;
    private readonly Mock<ILogger<DotAdminController>> _loggerMock;
    private readonly DotAdminController _controller;

    public DotAdminControllerTests()
    {
      _serviceMock = new Mock<IDotAdminService>();
      _loggerMock = new Mock<ILogger<DotAdminController>>();
      _controller = new DotAdminController(_serviceMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task CreateVehicleFromICVehicle_WithValidRequest_ReturnsOkResult()
    {
      // Arrange
      var request = new CreateVehicleFromICVehicleRequest
      {
        ICResponseId = Guid.NewGuid(),
        ICVehicleId = Guid.NewGuid(),
        LocationId = 123,
        CustomerId = 456
      };

      var expectedVehicle = new DotAdminVehicle
      {
        Id = "12345",
        Registration = "AB12 CDE",
        TypeId = 1,
        Flags = new DotAdminVehicleFlags(),
        LossCategory = false
      };

      _serviceMock
        .Setup(x => x.CreateVehicleFromICVehicleAsync(
          request.ICResponseId,
          request.ICVehicleId,
          request.LocationId,
          request.CustomerId,
          It.IsAny<CancellationToken>()))
        .ReturnsAsync(expectedVehicle);

      // Act
      var result = await _controller.CreateVehicleFromICVehicle(request);

      // Assert
      var okResult = Assert.IsType<OkObjectResult>(result.Result);
      var vehicle = Assert.IsType<DotAdminVehicle>(okResult.Value);
      Assert.Equal("12345", vehicle.Id);
      Assert.Equal("AB12 CDE", vehicle.Registration);
    }

    [Fact]
    public async Task CreateVehicleFromICVehicle_WithInvalidRequest_ReturnsBadRequest()
    {
      // Arrange
      var request = new CreateVehicleFromICVehicleRequest
      {
        ICResponseId = Guid.NewGuid(),
        ICVehicleId = Guid.NewGuid(),
        LocationId = 123
      };

      _serviceMock
        .Setup(x => x.CreateVehicleFromICVehicleAsync(
          It.IsAny<Guid>(),
          It.IsAny<Guid>(),
          It.IsAny<int>(),
          It.IsAny<int?>(),
          It.IsAny<CancellationToken>()))
        .ThrowsAsync(new ArgumentException("Invalid ICVehicle ID"));

      // Act
      var result = await _controller.CreateVehicleFromICVehicle(request);

      // Assert
      var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
      Assert.Equal("Invalid ICVehicle ID", badRequestResult.Value);
    }

    [Fact]
    public async Task CreateVehicle_WithValidRequest_ReturnsOkResult()
    {
      // Arrange
      var request = new CreateVehicleRequest
      {
        Registration = "AB12 CDE",
        Vin = "1234567890",
        CustomerId = 123,
        LocationId = 456,
        UseLookup = true
      };

      var expectedVehicle = new DotAdminVehicle
      {
        Id = "12345",
        Registration = "AB12 CDE",
        TypeId = 1,
        Flags = new DotAdminVehicleFlags(),
        LossCategory = false
      };

      _serviceMock
        .Setup(x => x.CreateVehicleAsync(
          request.Registration,
          request.Vin,
          request.CustomerId,
          request.LocationId,
          request.UseLookup,
          It.IsAny<CancellationToken>()))
        .ReturnsAsync(expectedVehicle);

      // Act
      var result = await _controller.CreateVehicle(request);

      // Assert
      var okResult = Assert.IsType<OkObjectResult>(result.Result);
      var vehicle = Assert.IsType<DotAdminVehicle>(okResult.Value);
      Assert.Equal("12345", vehicle.Id);
      Assert.Equal("AB12 CDE", vehicle.Registration);
    }

    [Fact]
    public void GetAuthStatus_ReturnsAuthenticationStatus()
    {
      // Arrange
      _serviceMock.Setup(x => x.IsAuthenticated).Returns(true);
      _serviceMock.Setup(x => x.CurrentCustomerId).Returns(123);
      _serviceMock.Setup(x => x.CurrentLocationId).Returns(456);

      // Act
      var result = _controller.GetAuthStatus();

      // Assert
      var okResult = Assert.IsType<OkObjectResult>(result.Result);
      var authStatus = Assert.IsType<AuthStatusResponse>(okResult.Value);
      Assert.True(authStatus.IsAuthenticated);
      Assert.Equal(123, authStatus.CurrentCustomerId);
      Assert.Equal(456, authStatus.CurrentLocationId);
    }

    [Fact]
    public async Task Authenticate_WithSuccessfulAuthentication_ReturnsTrue()
    {
      // Arrange
      _serviceMock
        .Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
        .ReturnsAsync(true);

      // Act
      var result = await _controller.Authenticate();

      // Assert
      var okResult = Assert.IsType<OkObjectResult>(result.Result);
      var success = Assert.IsType<bool>(okResult.Value);
      Assert.True(success);
    }
  }
}
