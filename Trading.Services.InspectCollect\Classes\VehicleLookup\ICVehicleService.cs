using Amazon.CognitoIdentityProvider.Model.Internal.MarshallTransformations;
using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Trading.API.Data;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.AutoTrader;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.Extensions;
using Trading.Services.InspectCollect.Interfaces;
using ZoomNet.Models;

namespace Trading.Services.InspectCollect.Classes.VehicleLookup;

public class ICVehicleService : ICVehicleInterface
{
  private readonly TradingContext _dbContext;
  private readonly IMapper _mapper;

  public ICVehicleService(TradingContext dbContext, IMapper mapper)
  {
    _dbContext = dbContext;
    _mapper = mapper;
  }

  public async Task<ICCapData> CreateICCapData(Guid icVehicleId, ICVehicleEnquiryDTO dto, ICVehicleCheckResponseDTO valuationData, CancellationToken cancellationToken)
  {
    ICCapValuation valuation = new ICCapValuation()
    {
      ICVehicleId = icVehicleId,
      VIN = string.IsNullOrEmpty(dto.VIN) ? dto.VIN : dto.VIN.ToUpper(),
      VRM = string.IsNullOrEmpty(dto.VRM) ? dto.VRM : dto.VRM.ToUpper(),  
      Odometer = (int)(dto.Odometer ?? 0),
      CostWhenNew = valuationData.CapData.LatestICValuation.CostWhenNew,
      PriceClean = valuationData.CapData.LatestICValuation.PriceClean,
      PriceAvg = valuationData.CapData.LatestICValuation.PriceAvg,
      PriceBelow = valuationData.CapData.LatestICValuation.PriceBelow,
      PriceRetail = valuationData.CapData.LatestICValuation.PriceRetail,
      Added = DateTime.Now,
      Updated = DateTime.Now,
      StatusId = (uint)StatusEnum.Active,
    };

    ICCapProvenance provenance = new ICCapProvenance()
    {
      ICVehicleId = icVehicleId,
      VRM = string.IsNullOrEmpty(dto.VRM) ? dto.VRM : dto.VRM.ToUpper(),
      VIN = string.IsNullOrEmpty(dto.VIN) ? dto.VIN : dto.VIN.ToUpper(),
      Odometer = (int)(dto.Odometer ?? 0),
      Damage = valuationData.CapData.LatestICProvenance.Damage,
      Theft = valuationData.CapData.LatestICProvenance.Theft,
      Finance = valuationData.CapData.LatestICProvenance.Finance,
      Scrapped = valuationData.CapData.LatestICProvenance.Scrapped,
      Security = valuationData.CapData.LatestICProvenance.Security,
      Stolen = valuationData.CapData.LatestICProvenance.Stolen,
      Added = DateTime.Now,
      Updated = DateTime.Now,
      StatusId = (uint)StatusEnum.Active,
    };

    var capData = await _dbContext.ICCapDatas
      .Include(x => x.LatestICProvenance)
      .Include(x => x.LatestICValuation)
      .AsNoTracking()
      .Where(c => c.ICVehicleId == icVehicleId && c.StatusId == (uint)StatusEnum.Active)
      .FirstOrDefaultAsync(cancellationToken);

    if (capData == null)
    {
      capData = new ICCapData
      {
        ICVehicleId = icVehicleId,
        CapCode = valuationData.CapData.CapCode,
        Added = DateTime.Now,
        Updated = DateTime.Now,
        StatusId = (uint)StatusEnum.Active,
        LatestICValuation = valuation,
        LatestICProvenance = provenance
      };

      _dbContext.Add(capData);
    } 
    else     
    {
      if (capData.LatestICProvenance != null && 
        (
          capData.LatestICProvenance.Damage != valuationData.CapData.LatestICProvenance.Damage || 
          capData.LatestICProvenance.Finance != valuationData.CapData.LatestICProvenance.Finance || 
          capData.LatestICProvenance.Security != valuationData.CapData.LatestICProvenance.Security ||
          capData.LatestICProvenance.Scrapped != valuationData.CapData.LatestICProvenance.Scrapped ||
          capData.LatestICProvenance.Stolen != valuationData.CapData.LatestICProvenance.Stolen ||
          capData.LatestICProvenance.Theft != valuationData.CapData.LatestICProvenance.Theft
        )
        )
      {
        // If the provenance data has changed, we need to update it
        capData.LatestICProvenance = provenance;
      } 
      else if (capData.LatestICProvenance == null)
      {
        // if there is no provenance data, we need to set it
        capData.LatestICProvenance = provenance;
      }

      capData.LatestICValuation = valuation;
      capData.Updated = DateTime.Now;
    }

    // attach capData to vehicle entity 
    var vehicle = await _dbContext.FindAsync<ICVehicle>(icVehicleId);
    if (vehicle != null)
    {
      vehicle.CapData = capData;
    }
    
    await _dbContext.SaveChangesAsync(cancellationToken);

    return capData;
  }

  public async Task<ICVehicleDTO> GetExistingICVehicleData(ICVehicleEnquiryDTO dto, CancellationToken cancellationToken)
  {
    ICVehicle? vehicle = null;
    // Search by VIN if provided, otherwise by VRM
    if (!string.IsNullOrEmpty(dto.VIN))
    {
      vehicle = await _dbContext.ICVehicles
          .Include(x => x.AutoTraderData.LatestFeatureList.Features.Where(y => y.ResponseSelected))
          .Include(x => x.AutoTraderData.LatestVehicle)
          .Where(v => v.CreatedByResponseId == dto.ResponseId && v.VIN == dto.VIN && v.StatusId == (uint)StatusEnum.Active)
          .FirstOrDefaultAsync(cancellationToken);
    }
    if (vehicle == null && !string.IsNullOrEmpty(dto.VRM))
    {
      vehicle = await _dbContext.ICVehicles
          .Include(x => x.AutoTraderData.LatestFeatureList.Features.Where(y => y.ResponseSelected))
          .Include(x => x.AutoTraderData.LatestVehicle)
          .Where(v => v.CreatedByResponseId == dto.ResponseId && v.VRM == dto.VRM && v.StatusId == (uint)StatusEnum.Active)
          .FirstOrDefaultAsync(cancellationToken);
    }

    if (vehicle == null)
    {
      return null;
    }

    // Fix: Replace LatestVehicle with the correct AutoTrader vehicle for this enquiry VRM/VIN
    if (vehicle.AutoTraderData != null)
    {
      AutoTraderVehicle? correctAutoTraderVehicle = null;

      // Find the AutoTrader vehicle that matches the enquiry VRM or VIN
      if (!string.IsNullOrEmpty(dto.VIN))
      {
        correctAutoTraderVehicle = await _dbContext.AutoTraderVehicles
          .Where(av => av.ICVehicleId == vehicle.Id && av.Vin == dto.VIN)
          .FirstOrDefaultAsync(cancellationToken);
      }

      if (correctAutoTraderVehicle == null && !string.IsNullOrEmpty(dto.VRM))
      {
        correctAutoTraderVehicle = await _dbContext.AutoTraderVehicles
          .Where(av => av.ICVehicleId == vehicle.Id && av.Registration == dto.VRM)
          .FirstOrDefaultAsync(cancellationToken);
      }

      // Replace the LatestVehicle with the correct one for this enquiry
      if (correctAutoTraderVehicle != null)
      {
        vehicle.AutoTraderData.LatestVehicle = correctAutoTraderVehicle;
      }
    }

    return _mapper.Map<ICVehicleDTO>(vehicle);
  }

  public async Task<ICVehicleDTO> GetOrCreateICVehicleData(ICVehicleEnquiryDTO dto, ICVehicleLookupDataDTO vehicleData, CancellationToken cancellationToken)
  {
    ICVehicle? vehicle = null;

    // Search by VIN if provided, otherwise by VRM
    if (!string.IsNullOrEmpty(dto.VIN))
    {
      vehicle = await _dbContext.ICVehicles
          .Where(v => v.CreatedByResponseId == dto.ResponseId && v.VIN == dto.VIN && v.StatusId == (uint)StatusEnum.Active)
          .FirstOrDefaultAsync(cancellationToken);
    }

    if (vehicle == null && !string.IsNullOrEmpty(dto.VRM))
    {
      vehicle = await _dbContext.ICVehicles
          .Where(v => v.CreatedByResponseId == dto.ResponseId && v.VRM == dto.VRM && v.StatusId == (uint)StatusEnum.Active)
          .FirstOrDefaultAsync(cancellationToken);
    }

    // We already have a vehicle 
    if (vehicle != null)
    {
      // update the vehicle details from the lookup data if needed 
      if (vehicleData != null)
      {
        vehicle = _mapper.Map(vehicleData, vehicle);
        vehicle.Updated = DateTime.Now;
        await _dbContext.SaveChangesAsync(cancellationToken);
      }

      return _mapper.Map<ICVehicleDTO>(vehicle);
    }

    // create a vehicle record to go with it
    var createDTO = _mapper.Map<ICVehicleDTO>(vehicleData);
    createDTO.CreatedByResponseId = dto.ResponseId;
    createDTO.StatusId = (uint)StatusEnum.Active;

    // create and return when not found 
    var vehicleDTO = await Create(createDTO, cancellationToken);

    return vehicleDTO;
  }

  public async Task<ICVehicleDTO> Create(ICVehicleDTO dto, CancellationToken cancellationToken)
  {
    ICVehicle vehicle = _mapper.Map<ICVehicle>(dto);
    vehicle.Added = DateTime.Now;
    vehicle.Updated = DateTime.Now;

    _dbContext.ICVehicles.Add(vehicle);
    await _dbContext.SaveChangesAsync();

    return _mapper.Map<ICVehicleDTO>(vehicle);
  }

  public async Task<ICVehicleDTO> Patch(Guid? icVehicleId, JsonPatchDocument<ICVehicle> patch, CancellationToken cancellationToken, Guid? icResponseId)
  {
    /// This is a bit of a hybrid patch, but done for performance reasons, most of the time we'll be looking up the
    /// vehicle by icResponseId, so that's an optional parameter at the end
    
    ICVehicle? vehicle = null;

    if (icVehicleId.HasValue)
    {
      vehicle = await _dbContext.ICVehicles
        .Where(v => v.Id == icVehicleId.Value)
        .FirstOrDefaultAsync(cancellationToken);
    }
    else if (icResponseId.HasValue)
    {
      vehicle = await _dbContext.ICVehicles
        .Where(v => v.CreatedByResponseId == icResponseId.Value)
        .FirstOrDefaultAsync(cancellationToken);
    }

    if (vehicle == null)
    {
      throw new KeyNotFoundException($"ICVehicle with ID {icVehicleId} / {icResponseId} not found.");
    }

    patch.FilterPatch();
    patch.ApplyTo(vehicle);

    await _dbContext.SaveChangesAsync(cancellationToken);

    return _mapper.Map<ICVehicleDTO>(vehicle);
  }

  public async Task<ICVehicleDTO> PatchByResponseId(Guid icResponseId, JsonPatchDocument<ICVehicle> patch, CancellationToken cancellationToken)
  {
    return await Patch(null, patch, cancellationToken, icResponseId);
  }

  public async Task<ICVehicleDTO> GetExistingICVehicleData(Guid icVehicleId, CancellationToken cancellationToken)
  {
    var vehicle = await _dbContext.ICVehicles
      .Include(x => x.AutoTraderData.LatestFeatureList.Features.Where(y => y.ResponseSelected))
      .Include(x => x.AutoTraderData.LatestValuation)
      .Include(x => x.CapData.LatestICValuation)
      .Include(x => x.AutoTraderData.LatestVehicle)
      .Where(v => v.Id == icVehicleId)
      .FirstOrDefaultAsync(cancellationToken);

    return vehicle == null ? null : _mapper.Map<ICVehicleDTO>(vehicle);
  }

  /// <summary>
  /// Gets existing ICVehicle data with the correct AutoTrader vehicle for the specified VRM/VIN
  /// </summary>
  /// <param name="icVehicleId">The ICVehicle ID</param>
  /// <param name="vrm">Vehicle Registration Mark to find the correct AutoTrader vehicle</param>
  /// <param name="vin">Vehicle Identification Number to find the correct AutoTrader vehicle</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>ICVehicle data with correct AutoTrader vehicle for the specified VRM/VIN</returns>
  public async Task<ICVehicleDTO> GetExistingICVehicleData(Guid icVehicleId, string vrm, string vin, CancellationToken cancellationToken)
  {
    var vehicle = await _dbContext.ICVehicles
      .Include(x => x.AutoTraderData.LatestFeatureList.Features.Where(y => y.ResponseSelected))
      .Include(x => x.AutoTraderData.LatestValuation)
      .Include(x => x.CapData.LatestICValuation)
      .Include(x => x.AutoTraderData.LatestVehicle)
      .Where(v => v.Id == icVehicleId)
      .FirstOrDefaultAsync(cancellationToken);

    if (vehicle == null)
    {
      return null;
    }

    // Fix: Replace LatestVehicle with the correct AutoTrader vehicle for the specified VRM/VIN
    if (vehicle.AutoTraderData != null && (!string.IsNullOrEmpty(vrm) || !string.IsNullOrEmpty(vin)))
    {
      AutoTraderVehicle? correctAutoTraderVehicle = null;

      // Find the AutoTrader vehicle that matches the specified VRM or VIN
      if (!string.IsNullOrEmpty(vin))
      {
        correctAutoTraderVehicle = await _dbContext.AutoTraderVehicles
          .Where(av => av.ICVehicleId == vehicle.Id && av.Vin == vin)
          .FirstOrDefaultAsync(cancellationToken);
      }

      if (correctAutoTraderVehicle == null && !string.IsNullOrEmpty(vrm))
      {
        correctAutoTraderVehicle = await _dbContext.AutoTraderVehicles
          .Where(av => av.ICVehicleId == vehicle.Id && av.Registration == vrm)
          .FirstOrDefaultAsync(cancellationToken);
      }

      // Replace the LatestVehicle with the correct one for the specified VRM/VIN
      if (correctAutoTraderVehicle != null)
      {
        vehicle.AutoTraderData.LatestVehicle = correctAutoTraderVehicle;
      }
    }

    return _mapper.Map<ICVehicleDTO>(vehicle);
  }
}
