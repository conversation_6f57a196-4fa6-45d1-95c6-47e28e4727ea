using AutoMapper;
using System;
using Trading.API.Data.DTO.DotAdmin;
using Trading.API.Data.Models.InspectCollect.VehicleData;

namespace Trading.Services.Mapping
{
  /// <summary>
  /// AutoMapper profile for mapping between ICVehicle and dotAdmin models
  /// </summary>
  public class DotAdminMappingProfile : Profile
  {
    public DotAdminMappingProfile()
    {
      CreateMap<ICVehicle, DotAdminCreateVehicleRequest>()
        .ForMember(dest => dest.Save, opt => opt.MapFrom(src => "1"))
        .ForMember(dest => dest.MotorVehicleRegistration, opt => opt.MapFrom(src => src.VRM))
        .ForMember(dest => dest.MotorVehicleVin, opt => opt.MapFrom(src => src.VIN))
        .ForMember(dest => dest.Lookup, opt => opt.MapFrom(src => true))
        .ForMember(dest => dest.MotorVehicleManufacturer, opt => opt.MapFrom(src => src.MakeName))
        .ForMember(dest => dest.MotorVehicleModel, opt => opt.MapFrom(src => src.ModelName))
        .ForMember(dest => dest.MotorVehicleVariant, opt => opt.MapFrom(src => src.DerivName))
        .ForMember(dest => dest.MotorVehicleColour, opt => opt.MapFrom(src => src.Colour))
        .ForMember(dest => dest.MotorVehicleBodyStyle, opt => opt.MapFrom(src => src.BodyTypeName))
        .ForMember(dest => dest.MotorVehicleFuelType, opt => opt.MapFrom(src => src.FuelTypeName))
        .ForMember(dest => dest.MotorVehicleGearboxType, opt => opt.MapFrom(src => src.TransmissionTypeName))
        .ForMember(dest => dest.MotorVehicleYearOfManufacture, opt => opt.MapFrom(src => src.YearOfManufacture))
        .ForMember(dest => dest.MotorVehicleDoorCount, opt => opt.MapFrom(src => ParseIntOrNull(src.Doors)))
        .ForMember(dest => dest.MotorVehicleExactCc, opt => opt.MapFrom(src => ParseIntOrNull(src.EngineCC)))
        .ForMember(dest => dest.MotorVehicleBhp, opt => opt.MapFrom(src => ParseIntOrNull(src.BHP)))
        .ForMember(dest => dest.MotorVehicleCo2, opt => opt.MapFrom(src => ParseIntOrNull(src.CO2)))
        .ForMember(dest => dest.MotorVehicleFormerKeepers, opt => opt.MapFrom(src => ParseIntOrNull(src.PreviousKeepers)))
        .ForMember(dest => dest.MotorVehicleMileage, opt => opt.MapFrom(src => ParseIntOrNull(src.Odometer)))
        .ForMember(dest => dest.MotorVehicleFirstRegistered, opt => opt.MapFrom(src => FormatDateString(src.DateRegistered)))
        .ForMember(dest => dest.MotorVehicleMotExpires, opt => opt.MapFrom(src => FormatDateTime(src.MOTExpiryDate)))
        .ForMember(dest => dest.MotorVehicleTypeId, opt => opt.MapFrom(src => MapVehicleTypeId(src.VehicleTypeName)))
        .ForMember(dest => dest.MotorVehicleClassificationId, opt => opt.MapFrom(src => MapVehicleClassificationId(src.BodyTypeName, src.VehicleTypeName)))
        // Set default values for required fields that will be provided separately
        .ForMember(dest => dest.VendorId, opt => opt.Ignore())
        .ForMember(dest => dest.LogisticsLocationId, opt => opt.Ignore())
        // Ignore optional fields that we don't have data for
        .ForMember(dest => dest.VendorHold, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleTitle, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleMileagedenominator, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleMileagewarranted, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehiclePrevregistration, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleCapcode, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleCapid, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleCapvehicletype, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleEnginesize, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleGrossvehicleweight, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleLength, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleWidth, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleHeight, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleAxles, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleSeatingcapacity, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleDvlatypeapproval, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleStandardeuroemissions, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleFueldelivery, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleGears, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleKw, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleMpgurban, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleMpgextraurban, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleMpgcombined, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleTaxdue, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleReserve, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleStandInValue, opt => opt.Ignore())
        .ForMember(dest => dest.MotorVehicleComments, opt => opt.Ignore());
    }

    /// <summary>
    /// Parse string to nullable int, returning null if parsing fails
    /// </summary>
    private static int? ParseIntOrNull(string value)
    {
      if (string.IsNullOrWhiteSpace(value))
        return null;

      return int.TryParse(value, out var result) ? result : null;
    }

    /// <summary>
    /// Format date string for dotAdmin API
    /// </summary>
    private static string FormatDateString(string dateString)
    {
      if (string.IsNullOrWhiteSpace(dateString))
        return null;

      if (DateTime.TryParse(dateString, out var date))
        return date.ToString("yyyy-MM-dd");

      return null;
    }

    /// <summary>
    /// Format DateTime for dotAdmin API
    /// </summary>
    private static string FormatDateTime(DateTime dateTime)
    {
      if (dateTime == default(DateTime))
        return null;

      return dateTime.ToString("yyyy-MM-dd");
    }

    /// <summary>
    /// Map vehicle type name to dotAdmin vehicle type ID
    /// </summary>
    private static int? MapVehicleTypeId(string vehicleTypeName)
    {
      var vehicleType = DotAdminEnumHelper.MapVehicleType(vehicleTypeName);
      return vehicleType.HasValue ? (int)vehicleType.Value : null;
    }

    /// <summary>
    /// Map body type and vehicle type to dotAdmin classification ID
    /// </summary>
    private static int? MapVehicleClassificationId(string bodyTypeName, string vehicleTypeName)
    {
      var classification = DotAdminEnumHelper.MapVehicleClassification(bodyTypeName, vehicleTypeName);
      return classification.HasValue ? (int)classification.Value : null;
    }
  }

  /// <summary>
  /// Extension methods for ICVehicle to dotAdmin mapping
  /// </summary>
  public static class ICVehicleMappingExtensions
  {
    /// <summary>
    /// Convert ICVehicle to DotAdminCreateVehicleRequest using AutoMapper
    /// </summary>
    public static DotAdminCreateVehicleRequest ToDotAdminRequest(this ICVehicle icVehicle, IMapper mapper, int vendorId, int logisticsLocationId)
    {
      if (icVehicle == null)
        throw new ArgumentNullException(nameof(icVehicle));

      var request = mapper.Map<DotAdminCreateVehicleRequest>(icVehicle);
      request.VendorId = vendorId;
      request.LogisticsLocationId = logisticsLocationId;

      return request;
    }

    /// <summary>
    /// Validate that ICVehicle has required fields for dotAdmin
    /// </summary>
    public static void ValidateForDotAdmin(this ICVehicle icVehicle)
    {
      if (icVehicle == null)
        throw new ArgumentNullException(nameof(icVehicle));

      if (string.IsNullOrWhiteSpace(icVehicle.VRM))
        throw new InvalidOperationException("ICVehicle must have a valid VRM for dotAdmin submission");

      // Additional validation rules can be added here
    }

    /// <summary>
    /// Check if ICVehicle is suitable for auction submission
    /// </summary>
    public static bool IsSuitableForAuction(this ICVehicle icVehicle)
    {
      if (icVehicle == null)
        return false;

      // Basic checks
      if (string.IsNullOrWhiteSpace(icVehicle.VRM))
        return false;

      // Could add more business rules here, such as:
      // - Vehicle age limits
      // - Mileage limits
      // - Condition requirements
      // - etc.

      return true;
    }
  }
}
