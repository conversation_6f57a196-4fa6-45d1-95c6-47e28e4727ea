﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Models.InventData;

namespace Trading.Services.Interfaces.InventImports;

/// <summary>
/// Interface for communicating with the Auction API
/// </summary>
public interface IAuctionApiClient
{
  Task<bool> AuthenticateAsync(CancellationToken cancellationToken = default);
  Task<List<Auction>> GetUpcomingAuctionsAsync(CancellationToken cancellationToken = default);
  Task<List<AuctionLot>> GetLotsByAuctionIdAsync(string auctionId, int pageSize = 100, CancellationToken cancellationToken = default);

  Task<InventAuction> FetchAuctionWithLotsAsync(string auctionId, CancellationToken cancellationToken = default);

  /// <summary>
  /// Fetches all auctions and their lots one by one
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Dictionary of auctions with their corresponding lots</returns>
  Task<Dictionary<InventAuction, List<InventAuctionLot>>> FetchAllAuctionsWithLotsAsync(CancellationToken cancellationToken = default);
}