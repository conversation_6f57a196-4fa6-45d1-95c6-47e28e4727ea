# dotAdmin API Usage Examples

This document provides practical examples of how to use the dotAdmin API integration in various scenarios.

## Table of Contents

1. [Service Layer Examples](#service-layer-examples)
2. [Controller Examples](#controller-examples)
3. [HTTP Client Examples](#http-client-examples)
4. [Error Handling Examples](#error-handling-examples)
5. [Configuration Examples](#configuration-examples)

## Service Layer Examples

### Example 1: Push ICVehicle to Auction

```csharp
public class VehicleAuctionService
{
    private readonly IDotAdminService _dotAdminService;
    private readonly ILogger<VehicleAuctionService> _logger;

    public VehicleAuctionService(IDotAdminService dotAdminService, ILogger<VehicleAuctionService> logger)
    {
        _dotAdminService = dotAdminService;
        _logger = logger;
    }

    public async Task<string> PushVehicleToAuctionAsync(Guid icResponseId, Guid icVehicleId, int locationId)
    {
        try
        {
            _logger.LogInformation("Pushing ICVehicle {ICVehicleId} to auction", icVehicleId);

            // Create vehicle in dotAdmin auction
            var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
                icResponseId, 
                icVehicleId, 
                locationId);

            _logger.LogInformation("Successfully created vehicle {VehicleId} in dotAdmin", vehicle.Id);
            
            return vehicle.Id;
        }
        catch (DotAdminValidationException ex)
        {
            _logger.LogWarning(ex, "Validation failed for ICVehicle {ICVehicleId}", icVehicleId);
            throw new InvalidOperationException($"Vehicle data is invalid: {ex.Message}", ex);
        }
        catch (DotAdminVehicleCreationException ex)
        {
            _logger.LogError(ex, "Failed to create vehicle in dotAdmin");
            throw new InvalidOperationException($"Failed to create vehicle in auction: {ex.Message}", ex);
        }
    }
}
```

### Example 2: Batch Vehicle Processing

```csharp
public class BatchVehicleProcessor
{
    private readonly IDotAdminService _dotAdminService;
    private readonly TradingContext _context;
    private readonly ILogger<BatchVehicleProcessor> _logger;

    public async Task<BatchProcessResult> ProcessVehicleBatchAsync(List<Guid> icVehicleIds, int locationId)
    {
        var result = new BatchProcessResult();
        
        foreach (var icVehicleId in icVehicleIds)
        {
            try
            {
                // Get ICVehicle to validate it's suitable for auction
                var icVehicle = await _context.ICVehicles.FindAsync(icVehicleId);
                if (icVehicle == null)
                {
                    result.Failures.Add(new ProcessFailure(icVehicleId, "ICVehicle not found"));
                    continue;
                }

                // Check if vehicle is suitable for auction
                if (!icVehicle.IsSuitableForAuction())
                {
                    result.Skipped.Add(new ProcessSkip(icVehicleId, "Vehicle not suitable for auction"));
                    continue;
                }

                // Create in dotAdmin
                var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
                    Guid.NewGuid(), // Generate response ID for batch
                    icVehicleId, 
                    locationId);

                result.Successes.Add(new ProcessSuccess(icVehicleId, vehicle.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process ICVehicle {ICVehicleId}", icVehicleId);
                result.Failures.Add(new ProcessFailure(icVehicleId, ex.Message));
            }
        }

        return result;
    }
}
```

### Example 3: Vehicle Creation with Custom Data

```csharp
public async Task<DotAdminVehicle> CreateCustomVehicleAsync(VehicleCreationRequest request)
{
    // Authenticate if needed
    if (!_dotAdminService.IsAuthenticated)
    {
        var authSuccess = await _dotAdminService.AuthenticateAsync();
        if (!authSuccess)
        {
            throw new InvalidOperationException("Failed to authenticate with dotAdmin");
        }
    }

    // Create vehicle with basic information
    var vehicle = await _dotAdminService.CreateVehicleAsync(
        request.Registration,
        request.Vin,
        request.CustomerId,
        request.LocationId,
        useLookup: true); // Let dotAdmin populate details

    return vehicle;
}
```

## Controller Examples

### Example 1: Custom Controller Action

```csharp
[ApiController]
[Route("api/[controller]")]
public class VehicleManagementController : ControllerBase
{
    private readonly IDotAdminService _dotAdminService;

    [HttpPost("push-to-auction")]
    public async Task<IActionResult> PushToAuction([FromBody] PushToAuctionRequest request)
    {
        try
        {
            var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
                request.ICResponseId,
                request.ICVehicleId,
                request.LocationId,
                request.CustomerId);

            return Ok(new { 
                Success = true, 
                VehicleId = vehicle.Id,
                Registration = vehicle.Registration,
                Message = "Vehicle successfully pushed to auction"
            });
        }
        catch (DotAdminValidationException ex)
        {
            return BadRequest(new { Success = false, Error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { Success = false, Error = "Internal server error" });
        }
    }
}
```

## HTTP Client Examples

### Example 1: Direct API Calls

```csharp
// Using HttpClient to call the dotAdmin API endpoints
public class DotAdminApiClient
{
    private readonly HttpClient _httpClient;

    public async Task<string> CreateVehicleFromICVehicleAsync(Guid icVehicleId, int locationId)
    {
        var request = new
        {
            icResponseId = Guid.NewGuid(),
            icVehicleId = icVehicleId,
            locationId = locationId
        };

        var response = await _httpClient.PostAsJsonAsync(
            "/api/dotadmin/vehicles/from-ic-vehicle", 
            request);

        response.EnsureSuccessStatusCode();
        
        var result = await response.Content.ReadFromJsonAsync<DotAdminVehicle>();
        return result.Id;
    }
}
```

### Example 2: JavaScript/TypeScript Client

```typescript
// TypeScript client for calling dotAdmin API
export class DotAdminClient {
    constructor(private baseUrl: string) {}

    async createVehicleFromICVehicle(request: CreateVehicleFromICVehicleRequest): Promise<DotAdminVehicle> {
        const response = await fetch(`${this.baseUrl}/api/dotadmin/vehicles/from-ic-vehicle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    async getAuthStatus(): Promise<AuthStatusResponse> {
        const response = await fetch(`${this.baseUrl}/api/dotadmin/auth-status`);
        return await response.json();
    }
}

// Usage
const client = new DotAdminClient('https://api.yourapp.com');
const vehicle = await client.createVehicleFromICVehicle({
    icResponseId: '123e4567-e89b-12d3-a456-************',
    icVehicleId: '123e4567-e89b-12d3-a456-************',
    locationId: 12,
    customerId: 3787
});
```

## Error Handling Examples

### Example 1: Comprehensive Error Handling

```csharp
public async Task<Result<string>> SafeCreateVehicleAsync(Guid icVehicleId, int locationId)
{
    try
    {
        var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
            Guid.NewGuid(), icVehicleId, locationId);
        
        return Result<string>.Success(vehicle.Id);
    }
    catch (DotAdminAuthenticationException ex)
    {
        _logger.LogError(ex, "Authentication failed");
        return Result<string>.Failure("Authentication failed. Please check credentials.");
    }
    catch (DotAdminValidationException ex)
    {
        _logger.LogWarning(ex, "Validation failed for ICVehicle {ICVehicleId}", icVehicleId);
        return Result<string>.Failure($"Vehicle data is invalid: {ex.Message}");
    }
    catch (DotAdminVehicleCreationException ex)
    {
        _logger.LogError(ex, "Vehicle creation failed");
        return Result<string>.Failure($"Failed to create vehicle: {ex.Message}");
    }
    catch (DotAdminServiceUnavailableException ex)
    {
        _logger.LogError(ex, "dotAdmin service unavailable");
        return Result<string>.Failure("Auction service is currently unavailable. Please try again later.");
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Unexpected error creating vehicle");
        return Result<string>.Failure("An unexpected error occurred. Please contact support.");
    }
}
```

### Example 2: Retry Logic

```csharp
public async Task<DotAdminVehicle> CreateVehicleWithRetryAsync(Guid icVehicleId, int locationId, int maxRetries = 3)
{
    var retryCount = 0;
    
    while (retryCount < maxRetries)
    {
        try
        {
            return await _dotAdminService.CreateVehicleFromICVehicleAsync(
                Guid.NewGuid(), icVehicleId, locationId);
        }
        catch (DotAdminServiceUnavailableException) when (retryCount < maxRetries - 1)
        {
            retryCount++;
            var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount)); // Exponential backoff
            _logger.LogWarning("dotAdmin service unavailable, retrying in {Delay}s (attempt {Attempt}/{MaxRetries})", 
                delay.TotalSeconds, retryCount + 1, maxRetries);
            await Task.Delay(delay);
        }
        catch (DotAdminAuthenticationException) when (retryCount < maxRetries - 1)
        {
            retryCount++;
            _logger.LogWarning("Authentication failed, retrying (attempt {Attempt}/{MaxRetries})", 
                retryCount + 1, maxRetries);
            // Force re-authentication on next attempt
            await Task.Delay(TimeSpan.FromSeconds(1));
        }
    }
    
    throw new InvalidOperationException($"Failed to create vehicle after {maxRetries} attempts");
}
```

## Configuration Examples

### Example 1: Environment-Specific Configuration

```json
// appsettings.Development.json
{
  "DotAdmin": {
    "BaseUrl": "https://dev-stack-admin.dotadmin.net",
    "Username": "<EMAIL>",
    "Password": "dev-password",
    "DefaultCustomerId": 3787,
    "DefaultLocationId": 12,
    "TimeoutSeconds": 60
  }
}

// appsettings.Production.json
{
  "DotAdmin": {
    "BaseUrl": "https://admin.dotadmin.net",
    "Username": "<EMAIL>",
    "Password": "#{DotAdminPassword}#", // Token replacement in deployment
    "DefaultCustomerId": 1001,
    "DefaultLocationId": 5,
    "TimeoutSeconds": 30
  }
}
```

### Example 2: Configuration Validation

```csharp
public class DotAdminConfigurationValidator
{
    public static void ValidateConfiguration(DotAdminDTO config)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(config.BaseUrl))
            errors.Add("BaseUrl is required");

        if (string.IsNullOrWhiteSpace(config.Username))
            errors.Add("Username is required");

        if (string.IsNullOrWhiteSpace(config.Password))
            errors.Add("Password is required");

        if (config.DefaultCustomerId <= 0)
            errors.Add("DefaultCustomerId must be positive");

        if (config.DefaultLocationId <= 0)
            errors.Add("DefaultLocationId must be positive");

        if (config.TimeoutSeconds <= 0)
            errors.Add("TimeoutSeconds must be positive");

        if (errors.Any())
        {
            throw new DotAdminConfigurationException(
                $"Invalid dotAdmin configuration: {string.Join(", ", errors)}");
        }
    }
}
```

### Example 3: Health Check Usage

```csharp
// In Startup.cs or Program.cs
services.AddHealthChecks()
    .AddDotAdminHealthCheck(
        name: "dotadmin",
        failureStatus: HealthStatus.Degraded,
        tags: new[] { "external", "auction" },
        timeout: TimeSpan.FromSeconds(30));

// Custom health check endpoint
[HttpGet("health/dotadmin")]
public async Task<IActionResult> CheckDotAdminHealth()
{
    var healthCheck = HttpContext.RequestServices.GetRequiredService<DotAdminHealthCheck>();
    var result = await healthCheck.CheckHealthAsync(new HealthCheckContext());
    
    return result.Status switch
    {
        HealthStatus.Healthy => Ok(new { Status = "Healthy", Description = result.Description }),
        HealthStatus.Degraded => Ok(new { Status = "Degraded", Description = result.Description }),
        HealthStatus.Unhealthy => StatusCode(503, new { Status = "Unhealthy", Description = result.Description }),
        _ => StatusCode(500, new { Status = "Unknown", Description = result.Description })
    };
}
```

## Best Practices

1. **Always handle exceptions** - Use the specific dotAdmin exception types for better error handling
2. **Check authentication status** - Verify authentication before making API calls
3. **Use validation** - Validate ICVehicle data before attempting to create vehicles
4. **Implement retry logic** - Handle transient failures with appropriate retry strategies
5. **Log appropriately** - Log important events but avoid logging sensitive data
6. **Monitor health** - Use health checks to monitor dotAdmin connectivity
7. **Configure timeouts** - Set appropriate timeout values for your environment
8. **Use batch processing** - For multiple vehicles, implement efficient batch processing

These examples should help you integrate the dotAdmin API effectively into your application.
