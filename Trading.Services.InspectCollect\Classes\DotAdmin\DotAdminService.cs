using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AutoMapper;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.Exceptions;
using Trading.Services.Mapping;
using Trading.API.Data;

namespace Trading.Services.InspectCollect.Classes.DotAdmin
{
  /// <summary>
  /// Service for dotAdmin business logic operations
  /// </summary>
  public class DotAdminService : IDotAdminService
  {
    private readonly IDotAdminClient _client;
    private readonly DotAdminDTO _config;
    private readonly ILogger<DotAdminService> _logger;
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;

    public DotAdminService(
      IDotAdminClient client,
      IOptionsSnapshot<DotAdminDTO> config,
      ILogger<DotAdminService> logger,
      TradingContext tradingContext,
      IMapper mapper)
    {
      _client = client ?? throw new ArgumentNullException(nameof(client));
      _config = config.Value ?? throw new ArgumentNullException(nameof(config));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _tradingContext = tradingContext ?? throw new ArgumentNullException(nameof(tradingContext));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }



    public async Task<DotAdminVehicle> CreateVehicleFromICVehicleAsync(
      Guid icResponseId,
      Guid icVehicleId, 
      int locationId, 
      int? customerId = null,
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle from ICVehicle {ICVehicleId} for response {ICResponseId}", 
        icVehicleId, icResponseId);

      // Get ICVehicle data
      var icVehicle = await _tradingContext.ICVehicles
        .Include(v => v.CapData)
        .Include(v => v.AutoTraderData)
        .FirstOrDefaultAsync(v => v.Id == icVehicleId, cancellationToken);

      if (icVehicle == null)
      {
        throw new ArgumentException($"ICVehicle with ID {icVehicleId} not found", nameof(icVehicleId));
      }

      // Validate required fields
      if (string.IsNullOrWhiteSpace(icVehicle.VRM))
      {
        throw new InvalidOperationException($"ICVehicle {icVehicleId} does not have a valid VRM");
      }

      // Use provided customer ID or default
      var targetCustomerId = customerId ?? _config.DefaultCustomerId;
      if (targetCustomerId == 0)
      {
        throw new InvalidOperationException("No customer ID provided and no default customer ID configured");
      }

      // Create the dotAdmin vehicle request using AutoMapper
      var request = icVehicle.ToDotAdminRequest(_mapper, targetCustomerId, locationId);

      try
      {
        var response = await _client.CreateVehicleAsync(request, cancellationToken);

        if (!response.Success || response.Vehicle == null)
        {
          throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
        }

        _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId} from ICVehicle {ICVehicleId}", 
          response.Vehicle.Id, icVehicleId);

        return response.Vehicle;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating dotAdmin vehicle from ICVehicle {ICVehicleId}", icVehicleId);
        throw;
      }
    }

    public async Task<DotAdminVehicle> CreateVehicleAsync(
      string registration, 
      string? vin, 
      int customerId, 
      int locationId, 
      bool useLookup = true, 
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle with registration {Registration}", registration);

      if (string.IsNullOrWhiteSpace(registration))
      {
        throw new ArgumentException("Registration cannot be null or empty", nameof(registration));
      }

      var request = new DotAdminCreateVehicleRequest
      {
        MotorVehicleRegistration = registration,
        MotorVehicleVin = vin,
        VendorId = customerId,
        LogisticsLocationId = locationId,
        Lookup = useLookup
      };

      var response = await _client.CreateVehicleAsync(request, cancellationToken);

      if (!response.Success || response.Vehicle == null)
      {
        throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
      }

      _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId}", response.Vehicle.Id);
      return response.Vehicle;
    }

    public async Task<DotAdminVehicle> CreateVehicleWithDetailsAsync(
      DotAdminCreateVehicleRequest request, 
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle with detailed request");

      var response = await _client.CreateVehicleAsync(request, cancellationToken);

      if (!response.Success || response.Vehicle == null)
      {
        throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
      }

      _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId}", response.Vehicle.Id);
      return response.Vehicle;
    }

    public async Task<List<DotAdminCustomer>> GetAvailableCustomersAsync(CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Getting available customers from dotAdmin");
      return new List<DotAdminCustomer>();
    }

    public async Task<Dictionary<string, string>> GetAvailableLocationsAsync(CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Getting available locations from dotAdmin");
      return new Dictionary<string, string>();
    }

    public async Task<bool> SelectCustomerLocationAsync(
      int customerId, 
      int locationId, 
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Selecting customer {CustomerId} and location {LocationId}", customerId, locationId);

        var response = await _client.SelectCustomerLocationAsync(customerId, locationId, cancellationToken);
        
        if (response.Success)
        {
          _logger.LogInformation("Successfully selected customer and location");
          return true;
        }

        _logger.LogWarning("Failed to select customer and location");
        return false;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error selecting customer and location");
        return false;
      }
    }
  }
}
