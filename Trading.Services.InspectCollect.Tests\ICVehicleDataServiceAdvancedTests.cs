﻿﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.API.Data.Models.AutoTrader;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.InspectCollect.Tests.Helpers;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.InspectCollect.Tests
{
    public class ICVehicleDataServiceAdvancedTests : TestBase
    {
        private readonly Mock<ICVehicleLookupInterface> _mockLookupService;
        private readonly Mock<IAutoTraderService> _mockAutoTraderService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ICVehicleInterface> _mockVehicleService;
        private readonly Mock<ILogger<ICVehicleDataService>> _mockLogger;
        private readonly Mock<IAutoTraderClient> _mockAutoTraderClient;
        private readonly Mock<ICResponseDataInterface> _mockResponseService;
        private readonly ICVehicleDataService _service;

        public ICVehicleDataServiceAdvancedTests()
        {
            _mockLookupService = MockHelpers.CreateMockLookupService();
            _mockAutoTraderService = MockHelpers.CreateMockAutoTraderService();
            _mockMapper = MockHelpers.CreateMockMapper();
            _mockVehicleService = MockHelpers.CreateMockVehicleService();
            _mockLogger = MockHelpers.CreateMockLogger();
            _mockAutoTraderClient = MockHelpers.CreateMockAutoTraderClient();
            _mockResponseService = MockHelpers.CreateMockResponseService();

            _service = new ICVehicleDataService(
                _mockLookupService.Object,
                _mockAutoTraderService.Object,
                _mockMapper.Object,
                _mockVehicleService.Object,
                _mockLogger.Object,
                _mockAutoTraderClient.Object,
                _mockResponseService.Object
            );
        }

        #region Vehicle Context Tests

        [Fact]
        public async Task GetFeatureValuationInternal_WithIncompleteVehicleContext_ReturnsFailure()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

            // Create a complete vehicle with all required data
            var vehicle = new ICVehicleDTO
            {
                Id = Guid.NewGuid(),
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                MakeName = "Test Make",
                ModelName = "Test Model",
                DerivName = "Test Derivative",
                CreatedByResponseId = Guid.NewGuid(),
                CapData = new ICCapDataDTO
                {
                    LatestICValuation = new ICCapValuationDTO
                    {
                        VRM = "TEST123",
                        VIN = "TESTVIN123456789",
                        Odometer = 50000
                    }
                },
                AutoTraderData = new ICAutoTraderDataDTO
                {
                    LatestFeatureList = new AutoTraderFeatureListDTO
                    {
                        Features = new List<AutoTraderFeatureDTO>
                        {
                            new AutoTraderFeatureDTO { Name = "Air Conditioning", ResponseSelected = true },
                            new AutoTraderFeatureDTO { Name = "Alloy Wheels", ResponseSelected = true }
                        }
                    },
                    LatestVehicle = new AutoTraderVehicleDTO
                    {
                        DerivativeId = "TEST_DERIV_123",
                        FirstRegistrationDate = DateTime.Now.AddYears(-3)
                    }
                }
            };

            var response = new ICResponseDTO
            {
                Id = Guid.NewGuid(),
                ICVehicleId = vehicle.Id.Value,
                ICVehicle = vehicle
            };

            _mockResponseService.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            // Act
            var result = await _service.GetFeatureValuationInternal(enquiry, CancellationToken.None);

            // Assert
            // The current implementation returns failure when vehicle context is incomplete
            // This is expected behavior when AutoTrader data is not properly set up
            Assert.False(result.Success);
            Assert.Equal("Incomplete feature data - missing derivative ID or first registration date", result.ErrorMessage);
        }

        [Fact]
        public async Task GetFeatureValuationInternal_WithIncompleteVehicleContext_StillProcesses()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
            var response = MockHelpers.CreateSampleResponseDTO();
            
            // Setup vehicle with incomplete data (missing derivative ID)
            response.ICVehicle.CapData = new ICCapDataDTO
            {
                LatestICValuation = new ICCapValuationDTO
                {
                    VRM = "TEST123",
                    VIN = "TESTVIN123456789",
                    Odometer = 50000
                }
            };

            // Add minimal AutoTrader data to avoid null reference
            response.ICVehicle.AutoTraderData = new ICAutoTraderDataDTO
            {
                LatestVehicle = new AutoTraderVehicleDTO
                {
                    DerivativeId = null, // Missing derivative ID to test incomplete data
                    FirstRegistrationDate = DateTime.Now.AddYears(-3)
                }
            };

            _mockResponseService.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            // Act
            var result = await _service.GetFeatureValuationInternal(enquiry, CancellationToken.None);

            // Assert
            // Should fail because derivative ID is missing
            Assert.False(result.Success);
            Assert.Equal("Incomplete feature data - missing derivative ID or first registration date", result.ErrorMessage);
        }

        #endregion

        #region Data Processing Tests

        [Fact]
        public async Task GetValuationDataInternal_WithOnlyCapDataSucceeding_ReturnsSuccess()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
            
            // Setup AutoTrader to fail but CAP to succeed
            _mockAutoTraderService.Setup(x => x.GetCombinedVehicleDataAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new HttpRequestException("AutoTrader service unavailable"));

            // Act
            var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.NotNull(result.Data.ICVehicle);
        }

        [Fact]
        public async Task GetValuationDataInternal_WithAutoTraderAndCapSucceeding_ProcessesBothDataTypes()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

            // Act
            var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.NotNull(result.Data.ICVehicle);

            // Verify both services were called
            _mockLookupService.Verify(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetCombinedVehicleDataAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Authentication Tests

        [Fact]
        public async Task GetAutoTraderValuationAsync_WhenAuthenticationSucceeds_CallsValuationService()
        {
            // Arrange
            var authResponse = new AuthTokenResponse { AccessToken = "valid-token", ExpiresAt = DateTime.UtcNow.AddHours(1) };
            _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(authResponse);

            // Act
            var result = await _service.GetAutoTraderValuationAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetVehicleValuationsAsync("TEST123", 50000, null, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAutoTraderFeaturesAsync_WhenAuthenticationSucceeds_CallsFeaturesService()
        {
            // Arrange
            var authResponse = new AuthTokenResponse { AccessToken = "valid-token", ExpiresAt = DateTime.UtcNow.AddHours(1) };
            _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(authResponse);

            // Act
            var result = await _service.GetAutoTraderFeaturesAsync("TEST123", CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetVehicleFeaturesAsync("TEST123", null, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAutoTraderMetricsAsync_WhenAuthenticationSucceeds_CallsMetricsService()
        {
            // Arrange
            var authResponse = new AuthTokenResponse { AccessToken = "valid-token", ExpiresAt = DateTime.UtcNow.AddHours(1) };
            _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(authResponse);

            // Act
            var result = await _service.GetAutoTraderMetricsAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetVehicleMetricsAsync("TEST123", 50000, null, It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public async Task GetAutoTraderValuationAsync_WhenServiceThrowsException_ReturnsFailure()
        {
            // Arrange
            _mockAutoTraderService.Setup(x => x.GetVehicleValuationsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new HttpRequestException("Service temporarily unavailable"));

            // Act
            var result = await _service.GetAutoTraderValuationAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("Service temporarily unavailable", result.ErrorMessage);
        }

        [Fact]
        public async Task GetAutoTraderFeaturesAsync_WhenServiceThrowsException_ReturnsFailure()
        {
            // Arrange
            _mockAutoTraderService.Setup(x => x.GetVehicleFeaturesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new TimeoutException("Request timeout"));

            // Act
            var result = await _service.GetAutoTraderFeaturesAsync("TEST123", CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("Request timeout", result.ErrorMessage);
        }

        [Fact]
        public async Task GetAutoTraderMetricsAsync_WhenServiceThrowsException_ReturnsFailure()
        {
            // Arrange
            _mockAutoTraderService.Setup(x => x.GetVehicleMetricsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Invalid vehicle data"));

            // Act
            var result = await _service.GetAutoTraderMetricsAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("Invalid vehicle data", result.ErrorMessage);
        }

        #endregion

        #region Logging Tests

        [Fact]
        public async Task GetLookupDataInternal_WhenValidationFails_LogsWarning()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateInvalidEnquiry();

            // Act
            var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            
            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("GetLookupDataInternal validation failed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetCapDataAsync_WhenValidationFails_LogsWarning()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateInvalidEnquiry();

            // Act
            var result = await _service.GetCapDataAsync(enquiry, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            
            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("GetCapDataAsync validation failed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        #endregion
    }
}
