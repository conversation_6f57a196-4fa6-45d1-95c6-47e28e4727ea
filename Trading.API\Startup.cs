using AutoMapper;
using Google.Apis.YouTube.v3;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.PlatformAbstractions;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using MySqlConnector;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.SwaggerUI;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Common;
using Trading.API.Common.APIKeyAuth;
using Trading.API.Common.Authorization;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Config;
using Trading.API.Remarq.ServiceConfiguration;
using Trading.Services.ExternalDTO;
using Trading.Services.ExternalDTO.Auth;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Helpers;
using Trading.Services.HubConfig;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.APIKeyAuth;

namespace Trading.API
{
  public static class AuthSchemes
  {
    public const string InspectCollect = "InspectCollect";
  }

  public class Startup
  {
    public Startup(IConfiguration configuration, IWebHostEnvironment env)
    {
      _currentEnvironment = env;
      Configuration = configuration;

      // Construct the appsettings filename based on the environment
      var appSettingsFileName = $"appsettings.{_currentEnvironment.EnvironmentName}.json";

      // Write to console
      Console.WriteLine($"Using app settings file: {appSettingsFileName}");
    }

    private IWebHostEnvironment _currentEnvironment;

    public IConfiguration Configuration { get; set; }

    public void ConfigureServices(IServiceCollection services)
    {

      Configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();

      var connection = Configuration.GetConnectionString("TradingAPIContext");

      SetupOptionsConfigDTOs(services);

      URLHelper.SetImageKitURL(Configuration.GetSection("ImageKit:URLSuffix").Value);
      URLHelper.SetInvoiceURL(Configuration.GetSection("S3Settings:InvoiceURL").Value);

      services.AddMemoryCache();

      services.RegisterCorePlatformServices(Configuration);

      // remarq only 
      services.Configure<VehicleVisionDTO>(Configuration.GetSection("VehicleVision"));
      services.Configure<SmartFleetDTO>(Configuration.GetSection("SmartFleetSettings"));
      services.RegisterModuleServices();
      // ***********

      services.AddDbContext<TradingContext>(opts =>
        opts.UseMySql(connection, ServerVersion.AutoDetect(connection))
      );

      // dapper connection
      services.AddScoped<IDbConnection>((sp) => new MySqlConnection(connection));

      // access user info in services
      services.AddHttpContextAccessor();
      services.AddHttpClient();

      var acceptedOrigins = Configuration.GetSection("Cors:Origins").Get<string[]>();

      services.AddCors(options =>
      {
        options.AddPolicy("CorsPolicy", builder => builder
          .WithOrigins(acceptedOrigins)
          .AllowAnyHeader()
          .WithExposedHeaders("Content-Disposition")
          .AllowAnyMethod()
          .AllowCredentials()
        );


        options.AddPolicy("SignalRPolicy", builder => builder
          .WithOrigins(acceptedOrigins)
          .WithMethods("GET", "POST")
          .AllowAnyHeader()
          .AllowCredentials()
        );
      });



      services.AddMvc().AddNewtonsoftJson();

      services.AddSignalR(options => { options.EnableDetailedErrors = true; });

      services.AddControllers()
        .AddNewtonsoftJson(options =>
        {
          options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
          options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
          options.SerializerSettings.StringEscapeHandling = StringEscapeHandling.EscapeNonAscii;
        })
        .AddJsonOptions(options =>
        {
          options.JsonSerializerOptions.PropertyNameCaseInsensitive = false;
          options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
          options.JsonSerializerOptions.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        });


      services.AddSwaggerGen(c =>
      {
        // Document definitions
        c.SwaggerDoc("RemarqInternal", new OpenApiInfo { Title = "Remarq API", Version = "v1" });
        c.SwaggerDoc("ApiKeyCustomer", new OpenApiInfo { Title = "API Key Customer", Version = "v1" });

        // API key security definition
        c.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
        {
          Description = "API key authentication",
          In = ParameterLocation.Header,
          Name = "X-API-Key",
          Type = SecuritySchemeType.ApiKey
        });

        // JWT bearer authentication to Swagger
        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
          Description = "JWT Authorization header using the Bearer scheme",
          Name = "Authorization",
          In = ParameterLocation.Header,
          Type = SecuritySchemeType.Http,
          Scheme = "bearer"
        });

        // Global security requirement for API Key
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "ApiKey"
                }
            },
            Array.Empty<string>()
        }
    });

        // Global security requirement for JWT
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

        // XML Comments
        var basePath = PlatformServices.Default.Application.ApplicationBasePath;
        var xmlPath = Path.Combine(basePath, "Trading.API.Remarq.xml");
        c.IncludeXmlComments(xmlPath);

        // Document inclusion logic
        c.DocInclusionPredicate((docName, apiDesc) =>
        {
          // First check if the controller has ApiExplorerSettings with IgnoreApi=true
          var ignoreApiAttribute = apiDesc.ActionDescriptor.EndpointMetadata
              .OfType<ApiExplorerSettingsAttribute>()
              .FirstOrDefault();

          if (ignoreApiAttribute?.IgnoreApi == true)
            return false;

          // For controllers with explicit SwaggerCustomer attributes, use those
          var actionAttributes = apiDesc.CustomAttributes().OfType<SwaggerCustomerAttribute>().ToArray();
          var controllerAttributes = apiDesc.ActionDescriptor.RouteValues.ContainsKey("controller")
              ? apiDesc.ActionDescriptor.EndpointMetadata.OfType<SwaggerCustomerAttribute>().ToArray()
              : Array.Empty<SwaggerCustomerAttribute>();

          if (docName == "ApiKeyCustomer" &&
              (actionAttributes.Any(attr => attr.Name == "ApiKeyCustomer") ||
               controllerAttributes.Any(attr => attr.Name == "ApiKeyCustomer")))
          {
            return true;
          }

          // For RemarqInternal, include everything by default unless it has a different SwaggerCustomer attribute
          if (docName == "RemarqInternal")
          {
            // If this has an explicit ApiKeyCustomer attribute, don't include it in RemarqInternal
            if (actionAttributes.Any(attr => attr.Name == "ApiKeyCustomer") ||
                controllerAttributes.Any(attr => attr.Name == "ApiKeyCustomer"))
            {
              return false;
            }

            // Include all other controllers by default
            return true;
          }

          return false;
        });

        // Add operation filter for API key security
        c.OperationFilter<ApiKeyOperationFilter>();
      });

      services.RegisterMappingProfiles();

      IFileProvider physicalProvider = new PhysicalFileProvider(Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), @"Content"));
      services.AddSingleton<IFileProvider>(physicalProvider);

      // Authentication configuration
      services.AddAuthentication(options =>
      {
        // Use a policy scheme to select between API key and JWT
        options.DefaultScheme = "AuthenticationScheme";
        options.DefaultChallengeScheme = "AuthenticationScheme";
      })
      .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
      {
        options.Audience = Configuration["CognitoAuth:Audience"];
        options.Authority = Configuration["CognitoAuth:Authority"];
        options.RequireHttpsMetadata = bool.Parse(Configuration["CognitoAuth:RequireHttpsMetadata"]);

        // Set the TokenValidationParameters explicitly
        options.TokenValidationParameters = new TokenValidationParameters
        {
          ValidateIssuer = true,
          ValidIssuer = Configuration["CognitoAuth:Authority"],  // This should be the Authority URL
          ValidateAudience = true,
          ValidAudience = Configuration["CognitoAuth:Audience"],
          ValidateLifetime = true,
          IssuerSigningKeyResolver = (token, securityToken, kid, validationParameters) =>
          {
            // This will be replaced by the injected HttpClient
            return Enumerable.Empty<SecurityKey>();
          }
        };

        options.Events = new JwtBearerEvents
        {
          OnAuthenticationFailed = context =>
          {
            Console.WriteLine("Authentication failed: " + context.Exception.Message);
            return Task.CompletedTask;
          },
          OnTokenValidated = async ctx =>
          {
            var newClaims = new List<Claim> { };
            var roleClaims = "";
            var identity = ctx.Principal.Identities.FirstOrDefault();

            /***********************************/
            /*** START OF IMPERSONATION CODE ***/
            /***********************************/

            var impersonate = ctx.Principal.Claims.Where(x => x.Type == "impersonate").FirstOrDefault();

            if (impersonate != null)
            {
              var impersonateClaims = JsonConvert.DeserializeObject<ImpersonateDTO>(impersonate.Value);

              var impersonator = identity.Claims.Where(x => x.Type == "contactId").FirstOrDefault().Value.ToString();

              var toReplace = new List<string>()
                      {
                    "contactId", "customerId", "contactStatusId", "customerStatusId", "customers", "userRoles",
                    "customerName", "impersonator"
                      };

              // Remove existing claims
              foreach (var claimName in toReplace)
              {
                Claim toDelete = identity.Claims.Where(x => x.Type == claimName).FirstOrDefault();
                if (toDelete != null)
                {
                  identity.RemoveClaim(toDelete);
                }
              }

              newClaims.Add(new Claim("contactId", impersonateClaims.ContactId.ToString()));
              newClaims.Add(new Claim("customerId", impersonateClaims.CustomerId.ToString()));
              newClaims.Add(new Claim("customerStatusId", impersonateClaims.CustomerStatusId.ToString()));
              newClaims.Add(new Claim("contactStatusId", impersonateClaims.ContactStatusId.ToString()));
              newClaims.Add(new Claim("customerName", impersonateClaims.CustomerName ?? ""));
              newClaims.Add(new Claim("customers", impersonateClaims.Customers ?? ""));
              newClaims.Add(new Claim("userRoles", impersonateClaims.UserRoles));
              newClaims.Add(new Claim("impersonator", impersonator));

              roleClaims = impersonateClaims.UserRoles;
            }
            else
            {
              roleClaims = "";

              var roleClaimResponse = ctx.Principal.Claims.Where(x => x.Type == "userRoles").FirstOrDefault();

              if (roleClaimResponse != null)
              {
                roleClaims = roleClaimResponse.Value.ToString();
              }
            }

            // Remarq
            foreach (var role in roleClaims.Split(","))
            {
              newClaims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Inspect-Collect
            foreach (var claim in ctx.Principal.Claims.Where(x => x.Type == "cognito:groups"))
            {
              newClaims.Add(new Claim(ClaimTypes.Role, claim.Value.ToUpper()));
            }

            var appIdentity = new ClaimsIdentity(newClaims);

            foreach (var cl in newClaims)
            {
              identity.AddClaim(cl);
            }
          },

          OnMessageReceived = context =>
          {
            var accessToken = context.Request.Query["access_token"];
            var path = context.HttpContext.Request.Path;

            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/messagehub"))
            {
              context.Token = accessToken;
            }

            return Task.CompletedTask;
          }
        };
      })
      .AddJwtBearer(AuthSchemes.InspectCollect, options =>
      {
        options.Audience = Configuration["CognitoAuth:InspectCollect:Audience"];
        options.Authority = Configuration["CognitoAuth:InspectCollect:Authority"];
        options.RequireHttpsMetadata = bool.Parse(Configuration["CognitoAuth:InspectCollect:RequireHttpsMetadata"]);

        options.TokenValidationParameters = new TokenValidationParameters
        {
          ValidateIssuer = true,
          ValidIssuer = Configuration["CognitoAuth:InspectCollect:Authority"],
          ValidateAudience = true,
          ValidAudience = Configuration["CognitoAuth:InspectCollect:Audience"],
          ValidateLifetime = true,
          IssuerSigningKeyResolver = (token, securityToken, kid, validationParameters) =>
          {
            return Enumerable.Empty<SecurityKey>();
          }
        };

        // Configure events for InspectCollect
        options.Events = new JwtBearerEvents
        {
          OnAuthenticationFailed = context =>
          {
            Console.WriteLine("InspectCollect authentication failed: " + context.Exception.Message);
            return Task.CompletedTask;
          },
          OnTokenValidated = async ctx =>
          {
            var newClaims = new List<Claim> { };
            var identity = ctx.Principal.Identities.FirstOrDefault();

            // Inspect-Collect specific claim handling
            foreach (var claim in ctx.Principal.Claims.Where(x => x.Type == "cognito:groups"))
            {
              newClaims.Add(new Claim(ClaimTypes.Role, claim.Value.ToUpper()));
            }

            foreach (var cl in newClaims)
            {
              identity.AddClaim(cl);
            }
          },
          OnMessageReceived = context =>
          {
            var accessToken = context.Request.Query["access_token"];
            var path = context.HttpContext.Request.Path;

            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/inspectcollect"))
            {
              context.Token = accessToken;
            }

            return Task.CompletedTask;
          }
        };
      })
      .AddPolicyScheme("AuthenticationScheme", "JWT or ApiKey", options =>
      {
        options.ForwardDefaultSelector = context =>
        {
          // Check if API key header is present
          if (context.Request.Headers.TryGetValue("X-API-Key", out var apiKeyHeaderValues) &&
        !string.IsNullOrEmpty(apiKeyHeaderValues.FirstOrDefault()))
          {
            return "ApiKey";
          }

          // Check if authorization header exists and starts with "Bearer"
          string authorization = context.Request.Headers["Authorization"];
          if (!string.IsNullOrEmpty(authorization) && authorization.StartsWith("Bearer "))
          {
            // For InspectCollect paths, use the InspectCollect JWT scheme
            if (context.Request.Path.StartsWithSegments("/api/inspect-collect"))
            {
              return AuthSchemes.InspectCollect;
            }

            // For other paths, use the default JWT scheme
            return JwtBearerDefaults.AuthenticationScheme;
          }

          // Default to JWT if no API key is present
          return JwtBearerDefaults.AuthenticationScheme;
        };
      });

      // Configure authorization with all policies
      services.AddAuthorization(options =>
      {
        // Add API key specific policies
        options.AddPolicy("ApiKeyPolicy", policy =>
        {
          policy.AddAuthenticationSchemes("ApiKey");
          policy.RequireAuthenticatedUser();
        });

        options.AddPolicy("TradingPlatform", policy =>
        {
          policy.AddAuthenticationSchemes("ApiKey");
          policy.RequireAuthenticatedUser();
          policy.AddRequirements(new RequireScopePolicy("TradingPlatform"));
        });

        options.AddPolicy("InspectCollect", policy =>
        {
          policy.AddAuthenticationSchemes("ApiKey");
          policy.RequireAuthenticatedUser();
          policy.AddRequirements(new RequireScopePolicy("InspectCollect"));
        });

        // Add a policy specific to InspectCollect JWT
        options.AddPolicy("RequireInspectCollect", policy =>
        {
          policy.RequireAuthenticatedUser();
          policy.AddAuthenticationSchemes(AuthSchemes.InspectCollect);
        });

        // Add the combined policy for JWT or API Key
        options.AddPolicy("JwtOrApiKeyForInspectCollect", policy =>
        {
          policy.RequireAuthenticatedUser();
          policy.AddRequirements(new JwtOrApiKeyRequirement(
              jwtScheme: AuthSchemes.InspectCollect,
              apiKeyScheme: "ApiKey",
              apiKeyScope: "InspectCollect"));
        });
      });

      services.AddResponseCompression(options =>
      {
        options.Providers.Add<GzipCompressionProvider>();
        options.EnableForHttps = true;
      });


      // Add Google authentication.
      services.AddAuthentication()
             .AddGoogle(options =>
             {
               options.ClientId = Configuration["YoutubeAuth:clientId"];
               options.ClientSecret = Configuration["YoutubeAuth:secret"];
               options.Scope.Add(YouTubeService.Scope.YoutubeUpload);
               options.Scope.Add(YouTubeService.Scope.YoutubeForceSsl);
             });

      services.AddOutputCache(options =>
      {
        options.AddPolicy("OutputCacheWithAuthPolicy", OutputCacheWithAuthPolicy.Instance);
        options.AddPolicy("ConditionalCacheWithAuthPolicy", ConditionalCacheWithAuthPolicy.Instance);
      });

      if (Configuration["Cron:Enabled"] == "true")
      {
        // services.AddHostedService<CronService>();

        if (Configuration["Xero:AutoRefreshToken"] == "true")
        {
          //services.AddHostedService<XeroRefreshService>();
          //services.AddHostedService<ProcessInvoicesService>();
        }

        //services.AddHostedService<ProcessImmediateSearchesService>();
        //services.AddHostedService<ProcessDailySearchesService>();
      }
    }

    private void SetupOptionsConfigDTOs(IServiceCollection services)
    {
      services.Configure<S3DTO>(Configuration.GetSection("S3Settings"));
      services.Configure<InspectCollectS3DTO>(Configuration.GetSection("InspectCollect"));
      services.Configure<AzureBlobDTO>(Configuration.GetSection("AzureStorageSettings"));
      services.Configure<DVLADTO>(Configuration.GetSection("DVLASettings"));
      services.Configure<CognitoDTO>(Configuration.GetSection("CognitoAuth"));
      services.Configure<EmailConfigDTO>(Configuration.GetSection("SendInBlue"));
      services.Configure<YoutubeDTO>(Configuration.GetSection("YoutubeAuth"));
      services.Configure<XeroDTO>(Configuration.GetSection("Xero"));
      services.Configure<StripeDTO>(Configuration.GetSection("Stripe"));
      services.Configure<SMSDTO>(Configuration.GetSection("SMS"));
      services.Configure<UKVehicleDTO>(Configuration.GetSection("UKVehicleData"));
      services.Configure<CapHPIDTO>(Configuration.GetSection("CapHPI"));
      services.Configure<ImageKitDTO>(Configuration.GetSection("ImageKit"));
      services.Configure<AuthConfigDTO>(Configuration.GetSection("Auth"));
      services.Configure<ArchiveConfigDTO>(Configuration.GetSection("Archive"));
      services.Configure<ServiceConfigDTO>(Configuration.GetSection("ServiceConfig"));
      services.Configure<KipperDTO>(Configuration.GetSection("Kippers"));
      services.Configure<DefaultPlatformDTO>(Configuration.GetSection("DefaultPlatform"));
      services.Configure<KlaviyoConfigDTO>(Configuration.GetSection("KlaviyoConfig"));
      services.Configure<PlateToVinDTO>(Configuration.GetSection("PlateToVin"));
      services.Configure<VinAuditDTO>(Configuration.GetSection("VinAudit"));
      services.Configure<VoiceHostConfigDTO>(Configuration.GetSection("VoiceHost"));
      services.Configure<FacebookDTO>(Configuration.GetSection("Facebook"));
      services.Configure<TwitterDTO>(Configuration.GetSection("Twitter"));
      services.Configure<LinkedInDTO>(Configuration.GetSection("LinkedIn"));
      services.Configure<ZoomDTO>(Configuration.GetSection("Zoom"));
      services.Configure<GoogleDTO>(Configuration.GetSection("Google"));
      services.Configure<PerplexityAIDTO>(Configuration.GetSection("Perplexity"));
      services.Configure<PortalConfigDTO>(Configuration.GetSection("Portal"));
      services.Configure<SQSDTO>(Configuration.GetSection("SQSSettings"));
      services.Configure<LambdaFunctionsConfigDTO>(Configuration.GetSection("LambdaFunctions"));
      services.Configure<ZohoCampaignConfigDTO>(Configuration.GetSection("ZohoCampaign"));
      services.Configure<InspectCollectCognitoDTO>(Configuration.GetSection("CognitoAuth:InspectCollect"));
      services.Configure<InventDTO>(Configuration.GetSection("InventApi"));
      services.Configure<AutoTraderDTO>(Configuration.GetSection("AutoTrader"));
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, TradingContext dataContext, IMapper mapper
    )
    {
      if (!env.IsDevelopment())
      {
        mapper.ConfigurationProvider.CompileMappings();
      }

      app.UseResponseCompression();

      if (env.IsDevelopment())
      {
        app.UseDeveloperExceptionPage();
      }

      app.UseSwagger();

      app.UseSwaggerUI(c =>
      {
        c.SwaggerEndpoint("/swagger/RemarqInternal/swagger.json", "Remarq API v1");
        c.SwaggerEndpoint("/swagger/ApiKeyCustomer/swagger.json", "API Key Customer v1");

        c.RoutePrefix = "swagger";

        // These are valid SwaggerUI options
        c.DocExpansion(DocExpansion.List);
        c.DefaultModelsExpandDepth(-1);

        // This line ensures the security options appear in the UI
        c.OAuthUseBasicAuthenticationWithAccessCodeGrant();
      });


      app.UseRouting();
      app.UseMiddleware<WebSocketsMiddleware>();

      app.UseCors("CorsPolicy");
      app.UseAuthentication();
      app.UseOutputCache();
      app.UseAuthorization();
      app.UseStaticFiles();

      // disable all caching
      app.Use(async (context, next) =>
      {
        context.Response.Headers[HeaderNames.CacheControl] = "no-cache, no-store, must-revalidate";
        context.Response.Headers[HeaderNames.Expires] = "0";
        context.Response.Headers[HeaderNames.Pragma] = "no-cache";

        await next();
      });

      dataContext.Database.SetCommandTimeout(3000);
      dataContext.Database.Migrate();
      dataContext.Database.EnsureCreated();

      // Don't run seed imports & dashboard stats every time in development
      if (!env.IsDevelopment()) { 
        dataContext.EnsureInitialData().Wait();
        RunStartupServiceMethodsAsync(app);
      }

      app.UseEndpoints(endpoints =>
      {
        endpoints.MapControllers().RequireCors("CorsPolicy");
        endpoints.MapHub<MessageHub>("/messagehub", options =>
        {
          options.Transports =
            Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets |
            Microsoft.AspNetCore.Http.Connections.HttpTransportType.LongPolling;
        }).RequireCors("SignalRPolicy");
      });
    }

    private static async Task RunStartupServiceMethodsAsync(IApplicationBuilder app)
    {
      using (var serviceScope = app.ApplicationServices.GetRequiredService<IServiceScopeFactory>().CreateScope())
      {
        var dashboardSevice = serviceScope.ServiceProvider.GetService<IDashboardService>();
        dashboardSevice.UpdateAdminDashboardStats(CancellationToken.None).Wait();

        //var apiKeyService = serviceScope.ServiceProvider.GetRequiredService<IApiKeyService>();
        //string apiKey = await apiKeyService.CreateApiKeyAsync(
        //    owner: "Demo Customer",
        //    scopes: new[] { "InspectCollect" },
        //    expiresAt: DateTime.UtcNow.AddYears(1)
        //);

        //var service = serviceScope.ServiceProvider.GetService<IVehicleMediaService>();
        //service.UploadWalkaroundVideo(null, CancellationToken.None);

        //var service = serviceScope.ServiceProvider.GetService<IAdvertSearchService>();
        //service.ProcessSearchNotifications(UpdateFrequencyEnum.Immediate, CancellationToken.None).Wait();

        //var service = serviceScope.ServiceProvider.GetService<IInvoiceService>();
        //service.ProcessInvoices(UpdateFrequencyEnum.Immediate, CancellationToken.None).Wait();
        //service.ProcessInvoices(UpdateFrequencyEnum.Daily, CancellationToken.None).Wait();
      }
    }

    public class ConditionalCacheWithAuthPolicy : IOutputCachePolicy
    {
      public static readonly ConditionalCacheWithAuthPolicy Instance = new();
      private ConditionalCacheWithAuthPolicy() { }

      ValueTask IOutputCachePolicy.CacheRequestAsync(OutputCacheContext context, CancellationToken cancellationToken)
      {
        var attemptOutputCaching = AttemptOutputCaching(context);
        context.EnableOutputCaching = true;
        context.AllowCacheLookup = attemptOutputCaching;
        context.AllowCacheStorage = attemptOutputCaching;
        context.AllowLocking = true;

        // Vary by any query by default
        context.CacheVaryByRules.QueryKeys = "*";
        return ValueTask.CompletedTask;
      }
      private static bool AttemptOutputCaching(OutputCacheContext context)
      {
        // Check if the current request fulfills the requirements to be cached
        var request = context.HttpContext.Request;

        // Verify the method, we only cache get and head verb
        if (!HttpMethods.IsGet(request.Method) && !HttpMethods.IsHead(request.Method))
        {
          return false;
        }

        if (context.HttpContext.Request.Query["cache"] == "true")
        {
          return true;
        }

        return false;
      }
      public ValueTask ServeFromCacheAsync(OutputCacheContext context, CancellationToken cancellation) => ValueTask.CompletedTask;
      public ValueTask ServeResponseAsync(OutputCacheContext context, CancellationToken cancellation) => ValueTask.CompletedTask;

    }


    public class OutputCacheWithAuthPolicy : IOutputCachePolicy
    {
      public static readonly OutputCacheWithAuthPolicy Instance = new();
      private OutputCacheWithAuthPolicy() { }

      ValueTask IOutputCachePolicy.CacheRequestAsync(OutputCacheContext context, CancellationToken cancellationToken)
      {
        var attemptOutputCaching = AttemptOutputCaching(context);
        context.EnableOutputCaching = true;
        context.AllowCacheLookup = attemptOutputCaching;
        context.AllowCacheStorage = attemptOutputCaching;
        context.AllowLocking = true;

        // Vary by any query by default
        context.CacheVaryByRules.QueryKeys = "*";
        return ValueTask.CompletedTask;
      }
      private static bool AttemptOutputCaching(OutputCacheContext context)
      {
        // Check if the current request fulfills the requirements to be cached
        var request = context.HttpContext.Request;

        // Verify the method, we only cache get and head verb
        if (!HttpMethods.IsGet(request.Method) && !HttpMethods.IsHead(request.Method))
        {
          return false;
        }
        // we comment out below code to cache authorization response.
        // Verify existence of authorization headers
        //if (!StringValues.IsNullOrEmpty(request.Headers.Authorization) || request.HttpContext.User?.Identity?.IsAuthenticated == true)
        //{
        //    return false;
        //}
        return true;
      }
      public ValueTask ServeFromCacheAsync(OutputCacheContext context, CancellationToken cancellation) => ValueTask.CompletedTask;
      public ValueTask ServeResponseAsync(OutputCacheContext context, CancellationToken cancellation) => ValueTask.CompletedTask;

    }

  }
}