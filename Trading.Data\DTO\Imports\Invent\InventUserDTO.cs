﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Imports.Invent;
public class InventUserDTO : BaseModelEntityDTO
{
  public string CompanyName { get; set; }

  public string AuctionId { get; set; }

  public DateTime? LastImportedDate { get; set; }


  public Guid ContactId { get; set; }
  public string ContactName { get; set; } 

  public Guid CustomerId { get; set; }
  public string CustomerName { get; set; }

  public Guid AddressId { get; set; }

  // if > 0 then logo swap is enabled and the value is the height in pixels to cut and paste the logo
  public int LogoSwapPixelHeight { get; set; }
}
