using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Xunit;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.Classes.DotAdmin;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces.DotAdmin;

namespace Trading.Services.DotAdmin.Tests.Integration
{
  /// <summary>
  /// Integration tests for dotAdmin API
  /// These tests require valid dotAdmin credentials and should be run against a sandbox environment
  /// </summary>
  [Collection("Integration Tests")]
  public class DotAdminIntegrationTests : IDisposable
  {
    private readonly IServiceProvider _serviceProvider;
    private readonly IDotAdminClient _client;
    private readonly DotAdminDTO _config;

    public DotAdminIntegrationTests()
    {
      // Setup configuration
      var configuration = new ConfigurationBuilder()
        .AddJsonFile("appsettings.test.json", optional: true)
        .AddEnvironmentVariables()
        .Build();

      // Setup DI container
      var services = new ServiceCollection();
      services.AddLogging(builder => builder.AddConsole());
      services.AddHttpClient<IDotAdminClient, DotAdminClient>();
      services.Configure<DotAdminDTO>(configuration.GetSection("DotAdmin"));

      _serviceProvider = services.BuildServiceProvider();
      _client = _serviceProvider.GetRequiredService<IDotAdminClient>();
      
      var options = _serviceProvider.GetRequiredService<IOptions<DotAdminDTO>>();
      _config = options.Value;
    }

    [Fact(Skip = "Integration test - requires valid credentials")]
    public async Task AuthenticateAsync_WithValidCredentials_ShouldSucceed()
    {
      // Skip if no credentials configured
      if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
      {
        return;
      }

      // Act
      var result = await _client.AuthenticateAsync(_config.Username, _config.Password);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Token);
      Assert.True(result.ExpiresAt > DateTimeOffset.UtcNow.ToUnixTimeSeconds());
    }

    [Fact(Skip = "Integration test - requires valid credentials")]
    public async Task CreateVehicleAsync_WithValidData_ShouldSucceed()
    {
      // Skip if no credentials configured
      if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
      {
        return;
      }

      // Arrange - First authenticate
      await _client.AuthenticateAsync(_config.Username, _config.Password, _config.DefaultCustomerId, _config.DefaultLocationId);

      var request = new DotAdminCreateVehicleRequest
      {
        MotorVehicleRegistration = "TEST123", // Use a test registration
        VendorId = _config.DefaultCustomerId,
        LogisticsLocationId = _config.DefaultLocationId,
        Lookup = true
      };

      // Act
      var result = await _client.CreateVehicleAsync(request);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Vehicle);
      Assert.NotNull(result.Vehicle.Id);
      Assert.Equal("TEST123", result.Vehicle.Registration);
    }

    [Fact(Skip = "Integration test - requires valid credentials")]
    public async Task SelectCustomerLocationAsync_WithValidData_ShouldSucceed()
    {
      // Skip if no credentials configured
      if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
      {
        return;
      }

      // Arrange - First authenticate without customer/location
      await _client.AuthenticateAsync(_config.Username, _config.Password);

      // Act
      var result = await _client.SelectCustomerLocationAsync(_config.DefaultCustomerId, _config.DefaultLocationId);

      // Assert
      Assert.True(result.Success);
      Assert.NotNull(result.Token);
    }

    [Fact(Skip = "Integration test - requires valid credentials")]
    public async Task FullWorkflow_AuthenticateAndCreateVehicle_ShouldSucceed()
    {
      // Skip if no credentials configured
      if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
      {
        return;
      }

      // Step 1: Authenticate
      var authResult = await _client.AuthenticateAsync(_config.Username, _config.Password);
      Assert.True(authResult.Success);

      // Step 2: Select customer and location if needed
      if (authResult.Next == "/loginselectcustomer")
      {
        var selectResult = await _client.SelectCustomerLocationAsync(_config.DefaultCustomerId, _config.DefaultLocationId);
        Assert.True(selectResult.Success);
      }

      // Step 3: Create a test vehicle
      var vehicleRequest = new DotAdminCreateVehicleRequest
      {
        MotorVehicleRegistration = $"TEST{DateTime.Now:HHmmss}", // Unique test registration
        VendorId = _config.DefaultCustomerId,
        LogisticsLocationId = _config.DefaultLocationId,
        Lookup = true
      };

      var vehicleResult = await _client.CreateVehicleAsync(vehicleRequest);
      Assert.True(vehicleResult.Success);
      Assert.NotNull(vehicleResult.Vehicle);
    }

    public void Dispose()
    {
      _client?.Dispose();
      _serviceProvider?.Dispose();
    }
  }

  /// <summary>
  /// Integration tests for the full service layer
  /// </summary>
  [Collection("Integration Tests")]
  public class DotAdminServiceIntegrationTests : IDisposable
  {
    private readonly IServiceProvider _serviceProvider;
    private readonly IDotAdminService _service;
    private readonly DotAdminDTO _config;

    public DotAdminServiceIntegrationTests()
    {
      // Setup configuration
      var configuration = new ConfigurationBuilder()
        .AddJsonFile("appsettings.test.json", optional: true)
        .AddEnvironmentVariables()
        .Build();

      // Setup DI container with all dependencies
      var services = new ServiceCollection();
      services.AddLogging(builder => builder.AddConsole());
      services.AddHttpClient<IDotAdminClient, DotAdminClient>();
      services.AddScoped<IDotAdminClient, DotAdminClient>();
      services.AddScoped<IDotAdminService, DotAdminService>();
      services.Configure<DotAdminDTO>(configuration.GetSection("DotAdmin"));

      // Add AutoMapper (would need actual profiles in real implementation)
      services.AddAutoMapper(typeof(DotAdminService));

      // Add in-memory database for testing
      services.AddDbContext<Trading.Data.TradingContext>(options =>
        options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

      _serviceProvider = services.BuildServiceProvider();
      _service = _serviceProvider.GetRequiredService<IDotAdminService>();
      
      var options = _serviceProvider.GetRequiredService<IOptions<DotAdminDTO>>();
      _config = options.Value;
    }

    [Fact(Skip = "Integration test - requires valid credentials")]
    public async Task AuthenticateAsync_WithConfiguredCredentials_ShouldSucceed()
    {
      // Skip if no credentials configured
      if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
      {
        return;
      }

      // Act
      var result = await _service.AuthenticateAsync();

      // Assert
      Assert.True(result);
      Assert.True(_service.IsAuthenticated);
    }

    [Fact(Skip = "Integration test - requires valid credentials")]
    public async Task CreateVehicleAsync_WithValidData_ShouldSucceed()
    {
      // Skip if no credentials configured
      if (string.IsNullOrEmpty(_config.Username) || string.IsNullOrEmpty(_config.Password))
      {
        return;
      }

      // Arrange
      await _service.AuthenticateAsync();

      // Act
      var result = await _service.CreateVehicleAsync(
        $"TEST{DateTime.Now:HHmmss}",
        "TEST123456789",
        _config.DefaultCustomerId,
        _config.DefaultLocationId);

      // Assert
      Assert.NotNull(result);
      Assert.NotNull(result.Id);
    }

    public void Dispose()
    {
      _serviceProvider?.Dispose();
    }
  }
}
