using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AutoMapper;
using Trading.API.Data.DTO.DotAdmin;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Data;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces.DotAdmin;
using Trading.Services.Exceptions;
using Trading.Services.Mapping;

namespace Trading.Services.Classes.DotAdmin
{
  /// <summary>
  /// Service for dotAdmin business logic operations
  /// </summary>
  public class DotAdminService : IDotAdminService
  {
    private readonly IDotAdminClient _client;
    private readonly DotAdminDTO _config;
    private readonly ILogger<DotAdminService> _logger;
    private readonly TradingContext _tradingContext;
    private readonly IMapper _mapper;

    public DotAdminService(
      IDotAdminClient client,
      IOptionsSnapshot<DotAdminDTO> config,
      ILogger<DotAdminService> logger,
      TradingContext tradingContext,
      IMapper mapper)
    {
      _client = client ?? throw new ArgumentNullException(nameof(client));
      _config = config.Value ?? throw new ArgumentNullException(nameof(config));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _tradingContext = tradingContext ?? throw new ArgumentNullException(nameof(tradingContext));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    public bool IsAuthenticated => _client.IsAuthenticated;

    public int? CurrentCustomerId => _client.CurrentCustomerId;

    public int? CurrentLocationId => _client.CurrentLocationId;

    public async Task<bool> AuthenticateAsync(CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Authenticating with dotAdmin service");

        var response = await _client.AuthenticateAsync(
          _config.Username,
          _config.Password,
          _config.DefaultCustomerId,
          _config.DefaultLocationId,
          cancellationToken);

        if (response.Success)
        {
          _logger.LogInformation("Successfully authenticated with dotAdmin");
          return true;
        }

        _logger.LogWarning("Authentication with dotAdmin failed");
        return false;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error during dotAdmin authentication");
        return false;
      }
    }

    public async Task<DotAdminVehicle> CreateVehicleFromICVehicleAsync(
      Guid icResponseId,
      Guid icVehicleId, 
      int locationId, 
      int? customerId = null,
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle from ICVehicle {ICVehicleId} for response {ICResponseId}", 
        icVehicleId, icResponseId);

      // Get ICVehicle data
      var icVehicle = await _tradingContext.ICVehicles
        .Include(v => v.CapData)
        .Include(v => v.AutoTraderData)
        .FirstOrDefaultAsync(v => v.Id == icVehicleId, cancellationToken);

      if (icVehicle == null)
      {
        throw new ArgumentException($"ICVehicle with ID {icVehicleId} not found", nameof(icVehicleId));
      }

      // Validate required fields
      if (string.IsNullOrWhiteSpace(icVehicle.VRM))
      {
        throw new InvalidOperationException($"ICVehicle {icVehicleId} does not have a valid VRM");
      }

      // Use provided customer ID or default
      var targetCustomerId = customerId ?? _config.DefaultCustomerId;
      if (targetCustomerId == 0)
      {
        throw new InvalidOperationException("No customer ID provided and no default customer ID configured");
      }

      // Create the dotAdmin vehicle request
      var request = CreateVehicleRequestFromICVehicle(icVehicle, targetCustomerId, locationId);

      try
      {
        var response = await _client.CreateVehicleAsync(request, cancellationToken);

        if (!response.Success || response.Vehicle == null)
        {
          throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
        }

        _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId} from ICVehicle {ICVehicleId}", 
          response.Vehicle.Id, icVehicleId);

        // TODO: Consider storing the dotAdmin vehicle ID back to ICVehicle or creating a mapping table
        // This would allow tracking which ICVehicles have been pushed to auction

        return response.Vehicle;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating dotAdmin vehicle from ICVehicle {ICVehicleId}", icVehicleId);
        throw;
      }
    }

    public async Task<DotAdminVehicle> CreateVehicleAsync(
      string registration, 
      string? vin, 
      int customerId, 
      int locationId, 
      bool useLookup = true, 
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle with registration {Registration}", registration);

      if (string.IsNullOrWhiteSpace(registration))
      {
        throw new ArgumentException("Registration cannot be null or empty", nameof(registration));
      }

      var request = new DotAdminCreateVehicleRequest
      {
        MotorVehicleRegistration = registration,
        MotorVehicleVin = vin,
        VendorId = customerId,
        LogisticsLocationId = locationId,
        Lookup = useLookup
      };

      var response = await _client.CreateVehicleAsync(request, cancellationToken);

      if (!response.Success || response.Vehicle == null)
      {
        throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
      }

      _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId}", response.Vehicle.Id);
      return response.Vehicle;
    }

    public async Task<DotAdminVehicle> CreateVehicleWithDetailsAsync(
      DotAdminCreateVehicleRequest request, 
      CancellationToken cancellationToken = default)
    {
      _logger.LogInformation("Creating dotAdmin vehicle with detailed request");

      var response = await _client.CreateVehicleAsync(request, cancellationToken);

      if (!response.Success || response.Vehicle == null)
      {
        throw new InvalidOperationException("Failed to create vehicle in dotAdmin");
      }

      _logger.LogInformation("Successfully created dotAdmin vehicle {VehicleId}", response.Vehicle.Id);
      return response.Vehicle;
    }

    public async Task<List<DotAdminCustomer>> GetAvailableCustomersAsync(CancellationToken cancellationToken = default)
    {
      // This would typically require a separate API call or be available from the auth response
      // For now, we'll return an empty list as the API documentation doesn't show a specific endpoint
      _logger.LogInformation("Getting available customers from dotAdmin");
      
      // If we have current auth, return customers from there
      if (_client.IsAuthenticated)
      {
        // We would need to store the auth response to access customers
        // This is a limitation of the current implementation
        return new List<DotAdminCustomer>();
      }

      return new List<DotAdminCustomer>();
    }

    public async Task<Dictionary<string, string>> GetAvailableLocationsAsync(CancellationToken cancellationToken = default)
    {
      // Similar to customers, this would come from the auth response
      _logger.LogInformation("Getting available locations from dotAdmin");
      
      return new Dictionary<string, string>();
    }

    public async Task<bool> SelectCustomerLocationAsync(
      int customerId, 
      int locationId, 
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Selecting customer {CustomerId} and location {LocationId}", customerId, locationId);

        var response = await _client.SelectCustomerLocationAsync(customerId, locationId, cancellationToken);
        
        if (response.Success)
        {
          _logger.LogInformation("Successfully selected customer and location");
          return true;
        }

        _logger.LogWarning("Failed to select customer and location");
        return false;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error selecting customer and location");
        return false;
      }
    }

    private DotAdminCreateVehicleRequest CreateVehicleRequestFromICVehicle(
      ICVehicle icVehicle, 
      int customerId, 
      int locationId)
    {
      var request = new DotAdminCreateVehicleRequest
      {
        // Required fields
        MotorVehicleRegistration = icVehicle.VRM,
        MotorVehicleVin = icVehicle.VIN,
        VendorId = customerId,
        LogisticsLocationId = locationId,
        Lookup = true, // Let dotAdmin populate most fields via lookup

        // Optional fields from ICVehicle data
        MotorVehicleManufacturer = icVehicle.MakeName,
        MotorVehicleModel = icVehicle.ModelName,
        MotorVehicleVariant = icVehicle.DerivName,
        MotorVehicleColour = icVehicle.Colour,
        MotorVehicleBodyStyle = icVehicle.BodyTypeName,
        MotorVehicleFuelType = icVehicle.FuelTypeName,
        MotorVehicleGearboxType = icVehicle.TransmissionTypeName,
        MotorVehicleYearOfManufacture = icVehicle.YearOfManufacture
      };

      // Parse numeric fields safely
      if (int.TryParse(icVehicle.Doors, out var doors))
        request.MotorVehicleDoorCount = doors;

      if (int.TryParse(icVehicle.EngineCC, out var engineCc))
        request.MotorVehicleExactCc = engineCc;

      if (int.TryParse(icVehicle.BHP, out var bhp))
        request.MotorVehicleBhp = bhp;

      if (int.TryParse(icVehicle.CO2, out var co2))
        request.MotorVehicleCo2 = co2;

      if (int.TryParse(icVehicle.PreviousKeepers, out var keepers))
        request.MotorVehicleFormerKeepers = keepers;

      if (int.TryParse(icVehicle.Odometer, out var mileage))
        request.MotorVehicleMileage = mileage;

      // Map vehicle type and classification
      var vehicleType = DotAdminEnumHelper.MapVehicleType(icVehicle.VehicleTypeName);
      if (vehicleType.HasValue)
        request.MotorVehicleTypeId = (int)vehicleType.Value;

      var classification = DotAdminEnumHelper.MapVehicleClassification(icVehicle.BodyTypeName, icVehicle.VehicleTypeName);
      if (classification.HasValue)
        request.MotorVehicleClassificationId = (int)classification.Value;

      // Format dates
      if (DateTime.TryParse(icVehicle.DateRegistered, out var regDate))
        request.MotorVehicleFirstRegistered = regDate.ToString("yyyy-MM-dd");

      if (icVehicle.MOTExpiryDate != default(DateTime))
        request.MotorVehicleMotExpires = icVehicle.MOTExpiryDate.ToString("yyyy-MM-dd");

      return request;
    }
  }
}
