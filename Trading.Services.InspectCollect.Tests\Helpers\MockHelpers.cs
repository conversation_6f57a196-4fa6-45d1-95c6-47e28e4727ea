﻿﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.API.Data.Models.AutoTrader;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.InspectCollect.Tests.Helpers
{
    public static class MockHelpers
    {
        public static Mock<ICVehicleLookupInterface> CreateMockLookupService()
        {
            var mock = new Mock<ICVehicleLookupInterface>();
            
            // Setup default successful responses
            mock.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ICVehicleLookupResult.CreateSuccess(CreateSampleLookupData()));
                
            mock.Setup(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ICCapDataResult.CreateSuccess(CreateSampleCapData()));
                
            return mock;
        }

        public static Mock<IAutoTraderService> CreateMockAutoTraderService()
        {
            var mock = new Mock<IAutoTraderService>();
            
            mock.Setup(x => x.GetVehicleValuationsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleValuationResponse());
                
            mock.Setup(x => x.GetVehicleFeaturesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleFeaturesResponse());
                
            mock.Setup(x => x.GetVehicleMetricsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleMetricsResponse());
                
            mock.Setup(x => x.GetCombinedVehicleDataAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleCombinedResponse());
                
            mock.Setup(x => x.GetVehicleValuationWithFeaturesAsync(
                It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<int>(), It.IsAny<List<string>>(),
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleValuationResponse());
                
            return mock;
        }

        public static Mock<IAutoTraderClient> CreateMockAutoTraderClient()
        {
            var mock = new Mock<IAutoTraderClient>();
            
            mock.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(new AuthTokenResponse { AccessToken = "test-token", ExpiresAt = DateTime.UtcNow.AddHours(1) });
                
            return mock;
        }

        public static Mock<ICVehicleInterface> CreateMockVehicleService()
        {
            var mock = new Mock<ICVehicleInterface>();
            
            mock.Setup(x => x.GetOrCreateICVehicleData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<ICVehicleLookupDataDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleVehicleDTO());
                
            mock.Setup(x => x.GetExistingICVehicleData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleVehicleDTO());
                
            mock.Setup(x => x.GetExistingICVehicleData(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleVehicleDTO());
                
            mock.Setup(x => x.CreateICCapData(It.IsAny<Guid>(), It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<ICVehicleCheckResponseDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleCapDataModel());
                
            return mock;
        }

        public static Mock<ICResponseDataInterface> CreateMockResponseService()
        {
            var mock = new Mock<ICResponseDataInterface>();
            
            mock.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleResponseDTO());
                
            return mock;
        }

        public static Mock<ILogger<ICVehicleDataService>> CreateMockLogger()
        {
            return new Mock<ILogger<ICVehicleDataService>>();
        }

        public static Mock<IMapper> CreateMockMapper()
        {
            var mock = new Mock<IMapper>();
            
            // Setup basic mapping behaviors
            mock.Setup(x => x.Map<ICCapDataDTO>(It.IsAny<ICCapData>()))
                .Returns(CreateSampleCapDataDTO());

            mock.Setup(x => x.Map<AutoTraderValuationDTO>(It.IsAny<ATVehicleValuationDTO>()))
                .Returns(CreateSampleAutoTraderValuationDTO());

            mock.Setup(x => x.Map<AutoTraderVehicleMetricDataDTO>(It.IsAny<ATVehicleMetricDTO>()))
                .Returns(CreateSampleAutoTraderVehicleMetricDataDTO());
                
            return mock;
        }

        // Sample data creation methods
        public static ICVehicleLookupDataDTO CreateSampleLookupData()
        {
            return new ICVehicleLookupDataDTO
            {
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                MakeName = "Test Make",
                ModelName = "Test Model",
                DerivName = "Test Derivative",
                ICVehicleId = Guid.NewGuid()
            };
        }

        public static ICVehicleCheckResponseDTO CreateSampleCapData()
        {
            return new ICVehicleCheckResponseDTO
            {
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                Odometer = 50000,
                LookupData = CreateSampleLookupData(),
                CapData = new ICCapDataDTO(),
                AutoTraderData = new ICAutoTraderDataDTO()
            };
        }

        public static ICVehicleDTO CreateSampleVehicleDTO()
        {
            return new ICVehicleDTO
            {
                Id = Guid.NewGuid(),
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                MakeName = "Test Make",
                ModelName = "Test Model",
                DerivName = "Test Derivative",
                CreatedByResponseId = Guid.NewGuid()
            };
        }

        public static ICResponseDTO CreateSampleResponseDTO()
        {
            return new ICResponseDTO
            {
                Id = Guid.NewGuid(),
                ICVehicleId = Guid.NewGuid(),
                ICVehicle = CreateSampleVehicleDTO()
            };
        }

        public static ATVehicleValuationsResponse CreateSampleValuationResponse()
        {
            return new ATVehicleValuationsResponse
            {
                Vehicle = new ATVehicleDTO { Registration = "TEST123", Vin = "TESTVIN123456789" },
                Valuations = new ATVehicleValuationDTO
                {
                    Trade = new ATValuationDTO { AmountGBP = 15000 },
                    PartExchange = new ATValuationDTO { AmountGBP = 16000 },
                    Retail = new ATValuationDTO { AmountGBP = 17000 },
                    Private = new ATPrivateValuationDTO { AmountGBP = 18000 }
                }
            };
        }

        public static ATVehicleFeaturesResponse CreateSampleFeaturesResponse()
        {
            return new ATVehicleFeaturesResponse
            {
                Vehicle = new ATVehicleDTO { Registration = "TEST123", Vin = "TESTVIN123456789" },
                Features = new List<ATVehicleFeatureDTO>
                {
                    new ATVehicleFeatureDTO { Name = "Air Conditioning", Category = "Comfort" },
                    new ATVehicleFeatureDTO { Name = "Alloy Wheels", Category = "Exterior" }
                }
            };
        }

        public static ATVehicleMetricsResponse CreateSampleMetricsResponse()
        {
            return new ATVehicleMetricsResponse
            {
                Vehicle = new ATVehicleDTO { Registration = "TEST123", Vin = "TESTVIN123456789" },
                VehicleMetrics = new ATVehicleMetricDTO
                {
                    Retail = new ATRetailMetricsDTO
                    {
                        Supply = new ATMetricValue { Value = 0.5 },
                        Demand = new ATMetricValue { Value = 0.7 },
                        MarketCondition = new ATMetricValue { Value = 0.6 },
                        Rating = new ATMetricValue { Value = 4.2 },
                        DaysToSell = new ATMetricValue { Value = 45 }
                    }
                }
            };
        }

        public static ATCombinedVehicleResponse CreateSampleCombinedResponse()
        {
            return new ATCombinedVehicleResponse
            {
                Vehicle = new ATVehicleDTO { Registration = "TEST123", Vin = "TESTVIN123456789" },
                Features = new List<ATVehicleFeatureDTO>
                {
                    new ATVehicleFeatureDTO { Name = "Air Conditioning", Category = "Comfort" }
                },
                History = new ATVehicleHistoryDTO(),
                Valuations = new ATVehicleValuationDTO(),
                VehicleMetrics = new ATVehicleMetricDTO()
            };
        }

        public static ICCapDataDTO CreateSampleCapDataDTO()
        {
            return new ICCapDataDTO
            {
                ICVehicleId = Guid.NewGuid()
            };
        }

        public static AutoTraderValuationDTO CreateSampleAutoTraderValuationDTO()
        {
            return new AutoTraderValuationDTO
            {
                Trade = 15000,
                PartExchange = 16000,
                Retail = 17000,
                Private = 18000,
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                Odometer = 50000
            };
        }

        public static List<AutoTraderFeatureDTO> CreateSampleAutoTraderFeaturesList()
        {
            return new List<AutoTraderFeatureDTO>
            {
                new AutoTraderFeatureDTO { Name = "Air Conditioning", Category = "Comfort", ResponseSelected = true },
                new AutoTraderFeatureDTO { Name = "Alloy Wheels", Category = "Exterior", ResponseSelected = true },
                new AutoTraderFeatureDTO { Name = "Bluetooth", Category = "Technology", ResponseSelected = false }
            };
        }

        public static ICCapData CreateSampleCapDataModel()
        {
            return new ICCapData
            {
                Id = Guid.NewGuid(),
                ICVehicleId = Guid.NewGuid(),
                CapCode = "TEST_CAP_CODE"
            };
        }

        public static AutoTraderVehicleMetricDataDTO CreateSampleAutoTraderVehicleMetricDataDTO()
        {
            return new AutoTraderVehicleMetricDataDTO
            {
                ICVehicleId = Guid.NewGuid(),
                Supply = 0.5,
                Demand = 0.7,
                MarketCondition = 0.6,
                Rating = 4.2,
                DaysToSell = 45
            };
        }
    }
}
