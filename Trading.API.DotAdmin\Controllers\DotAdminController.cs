using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.Interfaces.DotAdmin;

namespace Trading.API.DotAdmin.Controllers
{
  /// <summary>
  /// Controller for dotAdmin auction operations
  /// </summary>
  [ApiController]
  [Route("api/[controller]")]
  public class DotAdminController : ControllerBase
  {
    private readonly IDotAdminService _dotAdminService;
    private readonly ILogger<DotAdminController> _logger;

    public DotAdminController(IDotAdminService dotAdminService, ILogger<DotAdminController> logger)
    {
      _dotAdminService = dotAdminService ?? throw new ArgumentNullException(nameof(dotAdminService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Create a vehicle in dotAdmin auction from ICVehicle data
    /// </summary>
    /// <param name="request">Vehicle creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information</returns>
    [HttpPost("vehicles/from-ic-vehicle")]
    public async Task<ActionResult<DotAdminVehicle>> CreateVehicleFromICVehicle(
      [FromBody] CreateVehicleFromICVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Creating dotAdmin vehicle from ICVehicle {ICVehicleId}", request.ICVehicleId);

        var vehicle = await _dotAdminService.CreateVehicleFromICVehicleAsync(
          request.ICResponseId,
          request.ICVehicleId,
          request.LocationId,
          request.CustomerId,
          cancellationToken);

        return Ok(vehicle);
      }
      catch (ArgumentException ex)
      {
        _logger.LogWarning(ex, "Invalid request parameters");
        return BadRequest(ex.Message);
      }
      catch (InvalidOperationException ex)
      {
        _logger.LogWarning(ex, "Invalid operation");
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating vehicle from ICVehicle");
        return StatusCode(500, "An error occurred while creating the vehicle");
      }
    }

    /// <summary>
    /// Create a vehicle in dotAdmin auction with basic information
    /// </summary>
    /// <param name="request">Basic vehicle creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information</returns>
    [HttpPost("vehicles")]
    public async Task<ActionResult<DotAdminVehicle>> CreateVehicle(
      [FromBody] CreateVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Creating dotAdmin vehicle with registration {Registration}", request.Registration);

        var vehicle = await _dotAdminService.CreateVehicleAsync(
          request.Registration,
          request.Vin,
          request.CustomerId,
          request.LocationId,
          request.UseLookup,
          cancellationToken);

        return Ok(vehicle);
      }
      catch (ArgumentException ex)
      {
        _logger.LogWarning(ex, "Invalid request parameters");
        return BadRequest(ex.Message);
      }
      catch (InvalidOperationException ex)
      {
        _logger.LogWarning(ex, "Invalid operation");
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating vehicle");
        return StatusCode(500, "An error occurred while creating the vehicle");
      }
    }

    /// <summary>
    /// Create a vehicle in dotAdmin auction with detailed information
    /// </summary>
    /// <param name="request">Detailed vehicle creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information</returns>
    [HttpPost("vehicles/detailed")]
    public async Task<ActionResult<DotAdminVehicle>> CreateVehicleDetailed(
      [FromBody] DotAdminCreateVehicleRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        _logger.LogInformation("Creating dotAdmin vehicle with detailed request");

        var vehicle = await _dotAdminService.CreateVehicleWithDetailsAsync(request, cancellationToken);

        return Ok(vehicle);
      }
      catch (ArgumentException ex)
      {
        _logger.LogWarning(ex, "Invalid request parameters");
        return BadRequest(ex.Message);
      }
      catch (InvalidOperationException ex)
      {
        _logger.LogWarning(ex, "Invalid operation");
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error creating detailed vehicle");
        return StatusCode(500, "An error occurred while creating the vehicle");
      }
    }

    /// <summary>
    /// Get available customers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available customers</returns>
    [HttpGet("customers")]
    public async Task<ActionResult<List<DotAdminCustomer>>> GetCustomers(CancellationToken cancellationToken = default)
    {
      try
      {
        var customers = await _dotAdminService.GetAvailableCustomersAsync(cancellationToken);
        return Ok(customers);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error getting customers");
        return StatusCode(500, "An error occurred while getting customers");
      }
    }

    /// <summary>
    /// Get available locations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of location ID to location name</returns>
    [HttpGet("locations")]
    public async Task<ActionResult<Dictionary<string, string>>> GetLocations(CancellationToken cancellationToken = default)
    {
      try
      {
        var locations = await _dotAdminService.GetAvailableLocationsAsync(cancellationToken);
        return Ok(locations);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error getting locations");
        return StatusCode(500, "An error occurred while getting locations");
      }
    }

    /// <summary>
    /// Select customer and location for the session
    /// </summary>
    /// <param name="request">Customer and location selection request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    [HttpPost("select-customer-location")]
    public async Task<ActionResult<bool>> SelectCustomerLocation(
      [FromBody] SelectCustomerLocationRequest request,
      CancellationToken cancellationToken = default)
    {
      try
      {
        var success = await _dotAdminService.SelectCustomerLocationAsync(
          request.CustomerId,
          request.LocationId,
          cancellationToken);

        return Ok(success);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error selecting customer and location");
        return StatusCode(500, "An error occurred while selecting customer and location");
      }
    }

    /// <summary>
    /// Get authentication status
    /// </summary>
    /// <returns>Authentication status</returns>
    [HttpGet("auth-status")]
    public ActionResult<AuthStatusResponse> GetAuthStatus()
    {
      return Ok(new AuthStatusResponse
      {
        IsAuthenticated = _dotAdminService.IsAuthenticated,
        CurrentCustomerId = _dotAdminService.CurrentCustomerId,
        CurrentLocationId = _dotAdminService.CurrentLocationId
      });
    }

    /// <summary>
    /// Authenticate with dotAdmin
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication success status</returns>
    [HttpPost("authenticate")]
    public async Task<ActionResult<bool>> Authenticate(CancellationToken cancellationToken = default)
    {
      try
      {
        var success = await _dotAdminService.AuthenticateAsync(cancellationToken);
        return Ok(success);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error during authentication");
        return StatusCode(500, "An error occurred during authentication");
      }
    }
  }

  // Request/Response DTOs for the controller
  public class CreateVehicleFromICVehicleRequest
  {
    public Guid ICResponseId { get; set; }
    public Guid ICVehicleId { get; set; }
    public int LocationId { get; set; }
    public int? CustomerId { get; set; }
  }

  public class CreateVehicleRequest
  {
    public string Registration { get; set; } = string.Empty;
    public string? Vin { get; set; }
    public int CustomerId { get; set; }
    public int LocationId { get; set; }
    public bool UseLookup { get; set; } = true;
  }

  public class SelectCustomerLocationRequest
  {
    public int CustomerId { get; set; }
    public int LocationId { get; set; }
  }

  public class AuthStatusResponse
  {
    public bool IsAuthenticated { get; set; }
    public int? CurrentCustomerId { get; set; }
    public int? CurrentLocationId { get; set; }
  }
}
