﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.Models.InventData;

[Table("InventUser")]
public class InventUser : BaseModelEntity
{
  [MaxLength(32)]
  public string CompanyName { get; set; }

  [MaxLength(12)]
  public string AuctionId { get; set; }

  public DateTime? LastImportedDate { get; set; }


  public Guid ContactId { get; set; }
  public virtual Contact Contact { get; set; }

  public Guid CustomerId { get; set; }
  public virtual Customer Customer { get; set; }

  public Guid AddressId { get; set; }
  public virtual Address Address { get; set; }

  // if > 0 then logo swap is enabled and the value is the height in pixels to cut and paste the logo
  public int LogoSwapPixelHeight { get; set; }
}
