using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Trading.API.Data.DTO.DotAdmin
{
  /// <summary>
  /// Request model for creating a vehicle in dotAdmin
  /// </summary>
  public class DotAdminCreateVehicleRequest
  {
    [JsonPropertyName("save")]
    public string Save { get; set; } = "1";

    [JsonPropertyName("vendor_id")]
    public int VendorId { get; set; }

    [JsonPropertyName("logisticslocation_id")]
    public int LogisticsLocationId { get; set; }

    [JsonPropertyName("motorvehicle_registration")]
    public string MotorVehicleRegistration { get; set; }

    [JsonPropertyName("motorvehicle_vin")]
    public string MotorVehicleVin { get; set; }

    [JsonPropertyName("lookup")]
    public bool Lookup { get; set; } = true;

    // Optional fields - will be populated by lookup if not provided
    [JsonPropertyName("vendorhold")]
    public string VendorHold { get; set; }

    [JsonPropertyName("motorvehicletype_id")]
    public int? MotorVehicleTypeId { get; set; }

    [JsonPropertyName("motorvehicleclassification_id")]
    public int? MotorVehicleClassificationId { get; set; }

    [JsonPropertyName("motorvehicle_mileage")]
    public int? MotorVehicleMileage { get; set; }

    [JsonPropertyName("motorvehicle_title")]
    public string MotorVehicleTitle { get; set; }

    [JsonPropertyName("motorvehicle_manufacturer")]
    public string MotorVehicleManufacturer { get; set; }

    [JsonPropertyName("motorvehicle_model")]
    public string MotorVehicleModel { get; set; }

    [JsonPropertyName("motorvehicle_variant")]
    public string MotorVehicleVariant { get; set; }

    [JsonPropertyName("motorvehicle_colour")]
    public string MotorVehicleColour { get; set; }

    [JsonPropertyName("motorvehicle_bodystyle")]
    public string MotorVehicleBodyStyle { get; set; }

    [JsonPropertyName("motorvehicle_doorcount")]
    public int? MotorVehicleDoorCount { get; set; }

    [JsonPropertyName("motorvehicle_enginesize")]
    public string MotorVehicleEngineSize { get; set; }

    [JsonPropertyName("motorvehicle_exactcc")]
    public int? MotorVehicleExactCc { get; set; }

    [JsonPropertyName("motorvehicle_fueltype")]
    public string MotorVehicleFuelType { get; set; }

    [JsonPropertyName("motorvehicle_formerkeepers")]
    public int? MotorVehicleFormerKeepers { get; set; }

    [JsonPropertyName("motorvehicle_firstregistered")]
    public string MotorVehicleFirstRegistered { get; set; }

    [JsonPropertyName("motorvehicle_yearofmanufacture")]
    public int? MotorVehicleYearOfManufacture { get; set; }

    [JsonPropertyName("motorvehicle_gearboxtype")]
    public string MotorVehicleGearboxType { get; set; }

    [JsonPropertyName("motorvehicle_bhp")]
    public int? MotorVehicleBhp { get; set; }

    [JsonPropertyName("motorvehicle_co2")]
    public int? MotorVehicleCo2 { get; set; }

    [JsonPropertyName("motorvehicle_motexpires")]
    public string MotorVehicleMotExpires { get; set; }

    [JsonPropertyName("motorvehicle_reserve")]
    public string MotorVehicleReserve { get; set; }

    [JsonPropertyName("motorvehicle_standinvalue")]
    public string MotorVehicleStandInValue { get; set; }

    [JsonPropertyName("motorvehicle_comments")]
    public string MotorVehicleComments { get; set; }
  }

  /// <summary>
  /// Response model for vehicle creation in dotAdmin
  /// </summary>
  public class DotAdminCreateVehicleResponse
  {
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("vehicle")]
    public DotAdminVehicle Vehicle { get; set; }
  }

  /// <summary>
  /// Vehicle model from dotAdmin
  /// </summary>
  public class DotAdminVehicle
  {
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonPropertyName("type_id")]
    public int TypeId { get; set; }

    [JsonPropertyName("registration")]
    public string Registration { get; set; }

    [JsonPropertyName("flags")]
    public DotAdminVehicleFlags Flags { get; set; }

    [JsonPropertyName("losscategory")]
    public bool LossCategory { get; set; }

    /// <summary>
    /// Helper property to get ID as integer
    /// </summary>
    public int IdAsInt => int.TryParse(Id, out var result) ? result : 0;
  }

  /// <summary>
  /// Vehicle flags from dotAdmin
  /// </summary>
  public class DotAdminVehicleFlags
  {
    [JsonPropertyName("originAndUse")]
    public int OriginAndUse { get; set; }

    [JsonPropertyName("vcar_damage")]
    public int VcarDamage { get; set; }

    [JsonPropertyName("conditionAlert")]
    public int ConditionAlert { get; set; }

    [JsonPropertyName("scrapped")]
    public int Scrapped { get; set; }

    [JsonPropertyName("stolen")]
    public int Stolen { get; set; }

    [JsonPropertyName("mileage")]
    public int Mileage { get; set; }

    [JsonPropertyName("ieUsageAndTaxiCheck")]
    public int IeUsageAndTaxiCheck { get; set; }

    [JsonPropertyName("ukUsageAndTaxiCheck")]
    public int UkUsageAndTaxiCheck { get; set; }

    [JsonPropertyName("recallCheck")]
    public int RecallCheck { get; set; }

    [JsonPropertyName("keepers")]
    public int Keepers { get; set; }

    [JsonPropertyName("plateChange")]
    public int PlateChange { get; set; }

    [JsonPropertyName("colourChange")]
    public int ColourChange { get; set; }

    [JsonPropertyName("ieNctHistory")]
    public int IeNctHistory { get; set; }

    [JsonPropertyName("ieCvrtHistory")]
    public int IeCvrtHistory { get; set; }

    [JsonPropertyName("ukMotHistory")]
    public int UkMotHistory { get; set; }

    [JsonPropertyName("taxAndSorn")]
    public int TaxAndSorn { get; set; }

    [JsonPropertyName("regVoid")]
    public int RegVoid { get; set; }
  }
}
