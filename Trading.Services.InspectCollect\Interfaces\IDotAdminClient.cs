using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.DotAdmin;

namespace Trading.Services.InspectCollect.Interfaces
{
  /// <summary>
  /// Interface for dotAdmin API client operations
  /// </summary>
  public interface IDotAdminClient
  {
    /// <summary>
    /// Authenticate with dotAdmin API using username and password
    /// </summary>
    /// <param name="username">Username (typically email)</param>
    /// <param name="password">User password</param>
    /// <param name="customerId">Optional customer ID to select during authentication</param>
    /// <param name="locationId">Optional location ID to select during authentication</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication response with token and available customers/locations</returns>
    Task<DotAdminAuthResponse> AuthenticateAsync(
      string username, 
      string password, 
      int? customerId = null, 
      int? locationId = null, 
      CancellationToken cancellationToken = default);

    /// <summary>
    /// Select customer and location for the current session
    /// </summary>
    /// <param name="customerId">Customer ID to select</param>
    /// <param name="locationId">Location ID to select</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated authentication response with new token</returns>
    Task<DotAdminAuthResponse> SelectCustomerLocationAsync(
      int customerId, 
      int locationId, 
      CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a vehicle in dotAdmin
    /// </summary>
    /// <param name="request">Vehicle creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Vehicle creation response</returns>
    Task<DotAdminCreateVehicleResponse> CreateVehicleAsync(
      DotAdminCreateVehicleRequest request, 
      CancellationToken cancellationToken = default);

    /// <summary>
    /// Generic GET request to dotAdmin API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Deserialized response</returns>
    Task<T> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generic POST request to dotAdmin API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="data">Request data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Deserialized response</returns>
    Task<T> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if the client is currently authenticated
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// Get the current authentication token
    /// </summary>
    string? CurrentToken { get; }

    /// <summary>
    /// Get the current customer ID
    /// </summary>
    int? CurrentCustomerId { get; }

    /// <summary>
    /// Get the current location ID
    /// </summary>
    int? CurrentLocationId { get; }
  }
}
