using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Trading.API.Data.DTO.DotAdmin;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces.DotAdmin;
using Trading.Services.Exceptions;
using System.Net.Http.Headers;
using System.Net;

namespace Trading.Services.Classes.DotAdmin
{
  /// <summary>
  /// HTTP client for dotAdmin API operations
  /// </summary>
  public class DotAdminClient : IDotAdminClient, IDisposable
  {
    private readonly HttpClient _httpClient;
    private readonly DotAdminDTO _options;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly ILogger<DotAdminClient>? _logger;
    private DotAdminAuthResponse? _currentAuth;
    private bool _disposed = false;

    public DotAdminClient(HttpClient httpClient, IOptionsSnapshot<DotAdminDTO> options, ILogger<DotAdminClient>? logger = null)
    {
      _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
      _options = options.Value ?? throw new ArgumentNullException(nameof(options));
      _logger = logger;

      // Configure HttpClient
      _httpClient.BaseAddress = new Uri(_options.BaseUrl);
      _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);

      // Configure JSON serialization
      _jsonOptions = new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
      };
    }

    public bool IsAuthenticated => _currentAuth != null && !_currentAuth.IsExpiredOrExpiringSoon(_options.TokenRefreshBufferSeconds);

    public string? CurrentToken => _currentAuth?.Token;

    public int? CurrentCustomerId => _currentAuth != null && int.TryParse(_currentAuth.Token, out var customerId) ? customerId : null;

    public int? CurrentLocationId => _currentAuth != null && int.TryParse(_currentAuth.Token, out var locationId) ? locationId : null;

    public async Task<DotAdminAuthResponse> AuthenticateAsync(
      string username, 
      string password, 
      int? customerId = null, 
      int? locationId = null, 
      CancellationToken cancellationToken = default)
    {
      _logger?.LogInformation("Authenticating with dotAdmin API for user: {Username}", username);

      var request = new DotAdminAuthRequest
      {
        Username = username,
        Password = password,
        CustomerId = customerId,
        LocationId = locationId
      };

      try
      {
        var response = await PostAsync<DotAdminAuthResponse>("/login", request, cancellationToken);
        
        if (response.Success)
        {
          _currentAuth = response;
          _logger?.LogInformation("Successfully authenticated with dotAdmin API. Token expires at: {ExpirationTime}", 
            response.ExpirationDateTime);
        }
        else
        {
          _logger?.LogWarning("Authentication failed with dotAdmin API");
        }

        return response;
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "Error during dotAdmin authentication");
        throw;
      }
    }

    public async Task<DotAdminAuthResponse> SelectCustomerLocationAsync(
      int customerId, 
      int locationId, 
      CancellationToken cancellationToken = default)
    {
      _logger?.LogInformation("Selecting customer {CustomerId} and location {LocationId}", customerId, locationId);

      if (!IsAuthenticated)
      {
        throw new InvalidOperationException("Must be authenticated before selecting customer and location");
      }

      var request = new DotAdminCustomerLocationRequest
      {
        CustomerId = customerId,
        LocationId = locationId
      };

      try
      {
        var response = await PostAsync<DotAdminAuthResponse>("/loginselectcustomer", request, cancellationToken);
        
        if (response.Success)
        {
          _currentAuth = response;
          _logger?.LogInformation("Successfully selected customer and location. New token expires at: {ExpirationTime}", 
            response.ExpirationDateTime);
        }
        else
        {
          _logger?.LogWarning("Failed to select customer and location");
        }

        return response;
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "Error selecting customer and location");
        throw;
      }
    }

    public async Task<DotAdminCreateVehicleResponse> CreateVehicleAsync(
      DotAdminCreateVehicleRequest request, 
      CancellationToken cancellationToken = default)
    {
      _logger?.LogInformation("Creating vehicle in dotAdmin with registration: {Registration}", request.MotorVehicleRegistration);

      await EnsureAuthenticatedAsync(cancellationToken);

      try
      {
        var response = await PostAsync<DotAdminCreateVehicleResponse>(
          "/admin/auction/motorvehicles/createmotorvehicle", 
          request, 
          cancellationToken);

        if (response.Success)
        {
          _logger?.LogInformation("Successfully created vehicle with ID: {VehicleId}", response.Vehicle?.Id);
        }
        else
        {
          _logger?.LogWarning("Failed to create vehicle in dotAdmin");
        }

        return response;
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "Error creating vehicle in dotAdmin");
        throw;
      }
    }

    public async Task<T> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
      await EnsureAuthenticatedAsync(cancellationToken);

      var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
      await AddAuthenticationHeadersAsync(request);

      var response = await _httpClient.SendAsync(request, cancellationToken);
      response.EnsureSuccessStatusCode();

      var content = await response.Content.ReadAsStringAsync(cancellationToken);
      return JsonSerializer.Deserialize<T>(content, _jsonOptions)!;
    }

    public async Task<T> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default)
    {
      var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
      
      if (data != null)
      {
        var json = JsonSerializer.Serialize(data, _jsonOptions);
        request.Content = new StringContent(json, Encoding.UTF8, "application/json");
      }

      // Only add auth headers if we're not authenticating
      if (endpoint != "/login")
      {
        await EnsureAuthenticatedAsync(cancellationToken);
        await AddAuthenticationHeadersAsync(request);
      }

      var response = await _httpClient.SendAsync(request, cancellationToken);
      response.EnsureSuccessStatusCode();

      var content = await response.Content.ReadAsStringAsync(cancellationToken);
      return JsonSerializer.Deserialize<T>(content, _jsonOptions)!;
    }

    private async Task EnsureAuthenticatedAsync(CancellationToken cancellationToken = default)
    {
      if (!IsAuthenticated)
      {
        if (string.IsNullOrEmpty(_options.Username) || string.IsNullOrEmpty(_options.Password))
        {
          throw new InvalidOperationException("No valid authentication token and no credentials configured for automatic authentication");
        }

        await AuthenticateAsync(_options.Username, _options.Password, _options.DefaultCustomerId, _options.DefaultLocationId, cancellationToken);
      }
    }

    private async Task AddAuthenticationHeadersAsync(HttpRequestMessage request)
    {
      if (_currentAuth?.Token != null)
      {
        // dotAdmin supports both cookie and bearer token auth
        // We'll use bearer token for simplicity
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _currentAuth.Token);
      }
    }

    public void Dispose()
    {
      if (!_disposed)
      {
        _httpClient?.Dispose();
        _disposed = true;
      }
    }
  }
}
