using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.DotAdmin;

namespace Trading.Services.InspectCollect.Interfaces
{
  /// <summary>
  /// Service interface for dotAdmin business logic operations
  /// </summary>
  public interface IDotAdminService
  {

    /// <summary>
    /// Create a vehicle in dotAdmin auction from ICVehicle data
    /// </summary>
    /// <param name="icResponseId">ICResponse ID for tracking</param>
    /// <param name="icVehicleId">ICVehicle ID to get vehicle data from</param>
    /// <param name="locationId">Location ID for logistics location</param>
    /// <param name="customerId">Optional customer ID (uses default if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information</returns>
    Task<DotAdminVehicle> CreateVehicleFromICVehicleAsync(
      Guid icResponseId,
      Guid icVehicleId, 
      int locationId, 
      int? customerId = null,
      CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a vehicle in dotAdmin with basic registration/VIN information
    /// </summary>
    /// <param name="registration">Vehicle registration</param>
    /// <param name="vin">Vehicle VIN (optional)</param>
    /// <param name="customerId">Customer ID</param>
    /// <param name="locationId">Location ID</param>
    /// <param name="useLookup">Whether to use dotAdmin's lookup service to populate details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information</returns>
    Task<DotAdminVehicle> CreateVehicleAsync(
      string registration, 
      string? vin, 
      int customerId, 
      int locationId, 
      bool useLookup = true, 
      CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a vehicle in dotAdmin with detailed vehicle information
    /// </summary>
    /// <param name="request">Complete vehicle creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created vehicle information</returns>
    Task<DotAdminVehicle> CreateVehicleWithDetailsAsync(
      DotAdminCreateVehicleRequest request, 
      CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available customers for the authenticated user
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available customers</returns>
    Task<List<DotAdminCustomer>> GetAvailableCustomersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available locations for the authenticated user
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of location ID to location name</returns>
    Task<Dictionary<string, string>> GetAvailableLocationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Select customer and location for the current session
    /// </summary>
    /// <param name="customerId">Customer ID</param>
    /// <param name="locationId">Location ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if selection successful</returns>
    Task<bool> SelectCustomerLocationAsync(
      int customerId, 
      int locationId, 
      CancellationToken cancellationToken = default);


  }
}
