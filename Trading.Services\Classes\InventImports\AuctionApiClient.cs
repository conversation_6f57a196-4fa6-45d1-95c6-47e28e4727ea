﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.API.Data.Models.InventData;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces.InventImports;

namespace Trading.Services.Classes.InventImports;

/// <summary>
/// Client for interacting with the Auction API
/// </summary>
public class AuctionApiClient : IAuctionApiClient
{
  private readonly HttpClient _httpClient;
  private readonly ILogger<AuctionApiClient> _logger;
  private readonly InventDTO _configuration;
  private string _authToken;
  private DateTime _tokenExpiry = DateTime.MinValue;
  private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
  {
    PropertyNameCaseInsensitive = true,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
  };

  public AuctionApiClient(
      HttpClient httpClient,
      ILogger<AuctionApiClient> logger,
      IOptionsSnapshot<InventDTO> configuration)
  {
    _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));

    // Configure base URL from settings
    _httpClient.BaseAddress = new Uri(_configuration.BaseUrl);

    _jsonOptions.Converters.Add(new FlexibleStringConverter());
    _jsonOptions.Converters.Add(new FlexibleDateTimeConverter());
  }

  /// <summary>
  /// Authenticates with the Auction API and gets an access token
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>True if authentication was successful</returns>
  public async Task<bool> AuthenticateAsync(CancellationToken cancellationToken = default)
  {
    try
    {
      // Check if we already have a valid token
      if (!string.IsNullOrEmpty(_authToken) && _tokenExpiry > DateTime.UtcNow)
      {
        return true;
      }

      var username = _configuration.Username;
      var password = _configuration.Password;

      var authRequest = new
      {
        username,
        password
      };

      var content = new StringContent(
          JsonSerializer.Serialize(authRequest),
          Encoding.UTF8,
          "application/json");

      var response = await _httpClient.PostAsync("/api/webservicelogin", content, cancellationToken);

      if (!response.IsSuccessStatusCode)
      {
        _logger.LogError($"Failed to authenticate with the Auction API: {response.StatusCode}");
        return false;
      }

      var responseContent = await response.Content.ReadAsStringAsync();
      var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, new JsonSerializerOptions
      {
        PropertyNameCaseInsensitive = true
      });

      if (authResponse?.Status != true || string.IsNullOrEmpty(authResponse.UserToken))
      {
        _logger.LogError("Authentication response did not contain a valid token");
        return false;
      }

      _authToken = authResponse.UserToken;
      _tokenExpiry = DateTime.UtcNow.AddHours(1); // Token is valid for 1 hour according to the API docs

      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Exception occurred during authentication with the Auction API");
      return false;
    }
  }

  /// <summary>
  /// Gets a list of upcoming auctions
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of upcoming auctions</returns>
  public async Task<List<Auction>> GetUpcomingAuctionsAsync(CancellationToken cancellationToken = default)
  {
    await EnsureAuthenticatedAsync(cancellationToken);

    var response = await _httpClient.GetAsync("/api/auction/2.0/auctions", cancellationToken);

    if (!response.IsSuccessStatusCode)
    {
      _logger.LogError($"Failed to get upcoming auctions: {response.StatusCode}");
      return new List<Auction>();
    }

    var responseContent = await response.Content.ReadAsStringAsync();
    var auctionsResponse = JsonSerializer.Deserialize<AuctionsResponse>(responseContent, _jsonOptions);
    if (auctionsResponse?.Status != true || auctionsResponse.Auctions == null)
    {
      _logger.LogError("Auctions response was invalid");
      return new List<Auction>();
    }

    return auctionsResponse.Auctions;
  }

  /// <summary>
  /// Gets details for a specific auction by ID
  /// </summary>
  /// <param name="auctionId">ID of the auction</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Auction details</returns>
  private async Task<Auction> GetAuctionByIdAsync(string auctionId, CancellationToken cancellationToken = default)
  {
    await EnsureAuthenticatedAsync(cancellationToken);

    var response = await _httpClient.GetAsync($"/api/auction/2.0/auction/{auctionId}", cancellationToken);

    if (!response.IsSuccessStatusCode)
    {
      _logger.LogError($"Failed to get auction {auctionId}: {response.StatusCode}");
      return null;
    }

    var responseContent = await response.Content.ReadAsStringAsync();
    var auctionResponse = JsonSerializer.Deserialize<AuctionResponse>(responseContent, _jsonOptions);

    if (auctionResponse?.Status != true || auctionResponse.Auction == null)
    {
      _logger.LogError($"Auction response for {auctionId} was invalid");
      return null;
    }

    return auctionResponse.Auction;
  }

  public async Task<InventAuction> FetchAuctionWithLotsAsync(string auctionId, CancellationToken cancellationToken = default)
  {
    var result = new InventAuction();

    // Get all upcoming auctions
    var apiAuction = await GetAuctionByIdAsync(auctionId, cancellationToken);
    if (apiAuction == null)
    {
      _logger.LogWarning($"No auction found for id {apiAuction}");
      return result;
    }

    try
    {
      // Map to our entity
      var inventAuction = MapApiAuctionToInventAuction(apiAuction);

      // Get lots for this auction
      var lots = await GetLotsByAuctionIdAsync(auctionId, cancellationToken: cancellationToken);
      if (lots == null || lots.Count == 0)
      {
        _logger.LogInformation($"No lots found for auction {apiAuction.AuctionId}");
        return result;
      }

      _logger.LogInformation($"Found {lots.Count} lots for auction {apiAuction.AuctionId}");

      // Map each lot to our entity
      var inventLots = new List<InventAuctionLot>();
      foreach (var lot in lots)
      {
        var inventLot = MapApiLotToInventLot(lot);
        inventLot.AuctionId = inventAuction.Id;
        inventLots.Add(inventLot);
      }

      result.ActivityStatus = inventAuction.ActivityStatus;
      result.StatusId = inventAuction.StatusId;
      result.Added = inventAuction.Added;
      result.Updated = inventAuction.Updated;
      result.AllowStandOn = inventAuction.AllowStandOn;
      result.AuctionId = inventAuction.AuctionId;
      result.AuctionLocationId = inventAuction.AuctionLocationId;
      result.AuctionLocationTitle = inventAuction.AuctionLocationTitle;
      result.AuctionTypeTitle = inventAuction.AuctionTypeTitle;
      result.CurrentLotId = inventAuction.CurrentLotId;
      result.DateTime = inventAuction.DateTime;
      result.EndDateTime = inventAuction.EndDateTime;
      result.Information = inventAuction.Information;
      result.Title = inventAuction.Title;
      result.Lots = inventLots;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error processing auction {apiAuction.AuctionId}");
    }

    return result;
  }

  /// <summary>
  /// Fetches all auctions and their lots one by one
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Dictionary of auctions with their corresponding lots</returns>
  public async Task<Dictionary<InventAuction, List<InventAuctionLot>>> FetchAllAuctionsWithLotsAsync(CancellationToken cancellationToken = default)
  {
    var result = new Dictionary<InventAuction, List<InventAuctionLot>>();

    // Get all upcoming auctions
    var auctions = await GetUpcomingAuctionsAsync(cancellationToken);
    if (auctions == null || auctions.Count == 0)
    {
      _logger.LogWarning("No upcoming auctions found");
      return result;
    }

    _logger.LogInformation($"Found {auctions.Count} upcoming auctions to process");

    // Process each auction one by one
    foreach (var apiAuction in auctions)
    {
      try
      {
        _logger.LogInformation($"Processing auction {apiAuction.AuctionId}");

        // Get full auction details
        var auctionDetails = await GetAuctionByIdAsync(apiAuction.AuctionId, cancellationToken);
        if (auctionDetails == null)
        {
          _logger.LogWarning($"Could not retrieve details for auction {apiAuction.AuctionId}, skipping");
          continue;
        }

        // Map to our entity
        var inventAuction = MapApiAuctionToInventAuction(apiAuction);

        // Get lots for this auction
        var lots = await GetLotsByAuctionIdAsync(apiAuction.AuctionId, cancellationToken: cancellationToken);
        if (lots == null || lots.Count == 0)
        {
          _logger.LogInformation($"No lots found for auction {apiAuction.AuctionId}");
          result.Add(inventAuction, new List<InventAuctionLot>());
          continue;
        }

        _logger.LogInformation($"Found {lots.Count} lots for auction {apiAuction.AuctionId}");

        // Map each lot to our entity
        var inventLots = new List<InventAuctionLot>();
        foreach (var lot in lots)
        {
          var inventLot = MapApiLotToInventLot(lot);
          inventLot.AuctionId = inventAuction.Id;
          inventLots.Add(inventLot);
        }

        result.Add(inventAuction, inventLots);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error processing auction {apiAuction.AuctionId}");
      }
    }

    return result;
  }

  /// <summary>
  /// Gets lots for all upcoming auctions - now delegates to FetchAllAuctionsWithLotsAsync
  /// </summary>
  /// <param name="pageSize">Number of lots per page</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of lots</returns>
  public async Task<List<AuctionLot>> GetAllLotsAsync(int pageSize = 100, CancellationToken cancellationToken = default)
  {
    _logger.LogInformation("Getting all lots across all auctions");

    var allLots = new List<AuctionLot>();

    // Get all upcoming auctions
    var auctions = await GetUpcomingAuctionsAsync(cancellationToken);
    if (auctions == null || auctions.Count == 0)
    {
      _logger.LogWarning("No upcoming auctions found");
      return allLots;
    }

    // Process each auction one by one
    foreach (var auction in auctions)
    {
      try
      {
        var auctionLots = await GetLotsByAuctionIdAsync(auction.AuctionId, pageSize, cancellationToken);
        if (auctionLots != null && auctionLots.Count > 0)
        {
          _logger.LogInformation($"Adding {auctionLots.Count} lots from auction {auction.AuctionId}");
          allLots.AddRange(auctionLots);
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error getting lots for auction {auction.AuctionId}");
      }
    }

    _logger.LogInformation($"Retrieved a total of {allLots.Count} lots from all auctions");
    return allLots;
  }

  /// <summary>
  /// Gets lots for a specific auction
  /// </summary>
  /// <param name="auctionId">ID of the auction</param>
  /// <param name="pageSize">Number of lots per page</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of lots</returns>
  public async Task<List<AuctionLot>> GetLotsByAuctionIdAsync(string auctionId, int pageSize = 100, CancellationToken cancellationToken = default)
  {
    await EnsureAuthenticatedAsync(cancellationToken);

    var allLots = new List<AuctionLot>();
    var currentPage = 1;
    bool hasMorePages = true;

    while (hasMorePages)
    {
      var response = await _httpClient.GetAsync($"/api/auction/2.0/lots/{auctionId}?page={currentPage}&pagesize={pageSize}", cancellationToken);

      if (!response.IsSuccessStatusCode)
      {
        _logger.LogError($"Failed to get lots for auction {auctionId} page {currentPage}: {response.StatusCode}");
        break;
      }

      var responseContent = await response.Content.ReadAsStringAsync();
      var lotsResponse = JsonSerializer.Deserialize<LotsResponse>(responseContent, _jsonOptions);

      if (lotsResponse?.Status != true || lotsResponse.Lots == null)
      {
        _logger.LogError($"Lots response for auction {auctionId} page {currentPage} was invalid");
        break;
      }

      allLots.AddRange(lotsResponse.Lots);

      // Check if we have more pages to fetch
      if (lotsResponse.Pagination != null)
      {
        int totalPages = (int)Math.Ceiling((double)lotsResponse.Pagination.Total / lotsResponse.Pagination.PageSize);
        hasMorePages = currentPage < totalPages;
        currentPage++;

        // Safety check for empty results
        if (lotsResponse.Lots.Count == 0)
        {
          hasMorePages = false;
        }
      }
      else
      {
        hasMorePages = false;
      }
    }

    return allLots;
  }

  /// <summary>
  /// Ensures the client is authenticated before making API calls
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  private async Task EnsureAuthenticatedAsync(CancellationToken cancellationToken)
  {
    if (string.IsNullOrEmpty(_authToken) || _tokenExpiry <= DateTime.UtcNow)
    {
      bool authenticated = await AuthenticateAsync(cancellationToken);
      if (!authenticated)
      {
        throw new InvalidOperationException("Failed to authenticate with the Auction API");
      }
    }

    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _authToken);
  }

  private InventAuction MapApiAuctionToInventAuction(Auction apiAuction)
  {
    if (apiAuction == null)
      return null;

    var auction = new InventAuction
    {
      AuctionId = long.TryParse(apiAuction.Id, out long auctionId) ? auctionId : 0,
      Title = apiAuction.Title,
      DateTime = apiAuction.StartDate ??
                   (string.IsNullOrEmpty(apiAuction.DateTime) ? null :
                   DateTime.TryParse(apiAuction.DateTime, out DateTime dt) ? dt : null),
      EndDateTime = apiAuction.EndDate ??
                     (string.IsNullOrEmpty(apiAuction.EndDateTime) ? null :
                     DateTime.TryParse(apiAuction.EndDateTime, out DateTime edt) ? edt : null),
      ActivityStatus = int.TryParse(
            !string.IsNullOrEmpty(apiAuction.ActivityStatus) ?
            apiAuction.ActivityStatus : apiAuction.Status,
            out int activityStatus) ? activityStatus : 1,
      AllowStandOn = !string.IsNullOrEmpty(apiAuction.AllowStandOn) &&
            (apiAuction.AllowStandOn.Equals("true", StringComparison.OrdinalIgnoreCase) ||
             apiAuction.AllowStandOn.Equals("1")),
      CurrentLotId = int.TryParse(apiAuction.CurrentLot, out int currentLotId) ?
            currentLotId : null,
      Information = apiAuction.Information,
      AuctionLocationId = int.TryParse(apiAuction.AuctionLocationId, out int locationId) ?
            locationId : 0,
      AuctionLocationTitle = apiAuction.AuctionLocationTitle,
      AuctionTypeTitle = apiAuction.AuctionTypeTitle,
      Lots = new List<InventAuctionLot>(),
      StatusId = (uint)StatusEnum.Active,
      Added = DateTime.Now,
      Updated = DateTime.Now
    };

    auction.Id = Guid.NewGuid();

    return auction;
  }

  private InventAuctionLot MapApiLotToInventLot(AuctionLot apiLot)
  {
    if (apiLot == null)
      return null;

    var lot = new InventAuctionLot
    {
      // Use LotId from API, not AuctionLotId
      LotId = long.TryParse(apiLot.LotId, out long lotId) ? lotId : 0,

      // LotNumber should come from full details if available, not mileage
      LotNumber = apiLot.Full?.AuctionLotNumber != null &&
                  int.TryParse(apiLot.Full.AuctionLotNumber, out int lotNumber) ?
                  lotNumber : null,

      // Default status to 1 (Selling) if not provided
      Status = !string.IsNullOrEmpty(apiLot.AuctionLotStatus) &&
               int.TryParse(apiLot.AuctionLotStatus, out int status) ?
               status : 1,

      Type = "motorvehicle",

      Description = !string.IsNullOrEmpty(apiLot.Variant) ?
            $"{apiLot.Manufacturer} {apiLot.Model} {apiLot.Variant} {apiLot.Colour}" :
            $"{apiLot.Manufacturer} {apiLot.Model} {apiLot.Colour}",

      Vrm = apiLot.Vrm,
      Vin = apiLot.Vin,
      Manufacturer = apiLot.Manufacturer, // Fixed: was apiLot.Make
      Model = apiLot.Model,
      Variant = apiLot.Variant,

      Year = !string.IsNullOrEmpty(apiLot.Year) &&
             int.TryParse(apiLot.Year, out int year) ?
             year : null,

      Mileage = apiLot.Mileage,
      MileageDenominator = apiLot.Full?.MileageDenominator ?? "Miles",

      // Get MileageWarranted from Full details
      MileageWarranted = apiLot.Full?.MileageWarranted == "1" ||
                         apiLot.Full?.MileageWarranted?.Equals("true", StringComparison.OrdinalIgnoreCase) == true,

      FirstRegistered = !string.IsNullOrEmpty(apiLot.FirstRegDate) &&
            DateTime.TryParse(apiLot.FirstRegDate, out DateTime regDate) ?
            regDate : null,

      Colour = apiLot.Colour,
      FuelType = apiLot.FuelType,
      EngineSize = apiLot.EngineSize,
      BodyType = apiLot.BodyType,
      DoorCount = apiLot.DoorCount,
      Transmission = apiLot.Transmission,

      // Parse FormerKeeper from Full details
      FormerKeeper = apiLot.Full?.FormerKeepers != null &&
                     int.TryParse(apiLot.Full.FormerKeepers, out int formerKeepers) ?
                     formerKeepers : null,

      NamaGrade = apiLot.NamaGrade,

      // Parse CAP values - they come as strings but need to be decimals
      CapRetail = !string.IsNullOrEmpty(apiLot.CapRetail) &&
                  decimal.TryParse(apiLot.CapRetail, out decimal capRetail) ?
                  capRetail : null,

      CapClean = !string.IsNullOrEmpty(apiLot.CapClean) &&
                 decimal.TryParse(apiLot.CapClean, out decimal capClean) ?
                 capClean : null,

      CapAverage = !string.IsNullOrEmpty(apiLot.CapAverage) &&
                   decimal.TryParse(apiLot.CapAverage, out decimal capAverage) ?
                   capAverage : null,

      CapBelow = !string.IsNullOrEmpty(apiLot.CapBelow) &&
                 decimal.TryParse(apiLot.CapBelow, out decimal capBelow) ?
                 capBelow : null,

      // AutoTrader values
      AutotraderRetail = !string.IsNullOrEmpty(apiLot.AutotraderRetail) &&
                         decimal.TryParse(apiLot.AutotraderRetail, out decimal autoRetail) ?
                         autoRetail : null,

      AutotraderTrade = !string.IsNullOrEmpty(apiLot.AutotraderTrade) &&
                        decimal.TryParse(apiLot.AutotraderTrade, out decimal autoTrade) ?
                        autoTrade : null,

      ClassificationId = apiLot.ClassificationId,
      ClassificationTitle = apiLot.ClassificationTitle,
      VatStatus = apiLot.VatStatus,
      HasV5 = apiLot.HasV5, // This is now a bool in the API response
      V5OrderStatus = apiLot.V5OrderStatus,

      // Parse NonRunner - check both API level and Full details
      NonRunner = (!string.IsNullOrEmpty(apiLot.NonRunner) &&
                  (apiLot.NonRunner == "1" || apiLot.NonRunner.Equals("true", StringComparison.OrdinalIgnoreCase))),

      InspectionReportUrl = apiLot.InspectionReport,

      // Get CO2 from either level
      Co2 = !string.IsNullOrEmpty(apiLot.Co2) ? apiLot.Co2 : apiLot.Full?.Co2,

      StandardEuroEmissions = apiLot.Full?.StandardEuroEmissions,
      NumKeys = apiLot.Full?.NumKeys,
      ReservePrice = apiLot.ReservePrice,
      CurrentBid = apiLot.CurrentBid,
      SalePrice = null, // This would come from bidding/sale data
      SoldToCustomerId = null, // This would come from bidding/sale data

      // Initialize empty images collection - will be populated separately
      Images = new List<InventAuctionLotImage>(),
      StatusId = (uint)StatusEnum.Active,
      Added = DateTime.Now,
      Updated = DateTime.Now
    };

    // Enhanced Full details processing
    if (apiLot.Full != null)
    {
      // Override with more detailed information from Full if available
      if (string.IsNullOrEmpty(lot.Transmission) && !string.IsNullOrEmpty(apiLot.Full.GearboxType))
      {
        lot.Transmission = apiLot.Full.GearboxType;
      }

      // Use Full details for V5 status if main level is false
      if (!lot.HasV5 && !string.IsNullOrEmpty(apiLot.Full.HasV5))
      {
        lot.HasV5 = apiLot.Full.HasV5 == "1" ||
            apiLot.Full.HasV5.Equals("true", StringComparison.OrdinalIgnoreCase);
      }

      // Override CAP values from Full if main level is null
      if (lot.CapRetail == null && !string.IsNullOrEmpty(apiLot.Full.CapRetail))
      {
        lot.CapRetail = decimal.TryParse(apiLot.Full.CapRetail, out decimal fullCapRetail) ?
                        fullCapRetail : null;
      }

      if (lot.CapClean == null && !string.IsNullOrEmpty(apiLot.Full.CapClean))
      {
        lot.CapClean = decimal.TryParse(apiLot.Full.CapClean, out decimal fullCapClean) ?
                       fullCapClean : null;
      }

      if (lot.CapAverage == null && !string.IsNullOrEmpty(apiLot.Full.CapAverage))
      {
        lot.CapAverage = decimal.TryParse(apiLot.Full.CapAverage, out decimal fullCapAverage) ?
                         fullCapAverage : null;
      }

      if (lot.CapBelow == null && !string.IsNullOrEmpty(apiLot.Full.CapBelow))
      {
        lot.CapBelow = decimal.TryParse(apiLot.Full.CapBelow, out decimal fullCapBelow) ?
                       fullCapBelow : null;
      }
    }

    // Process images if available
    if (apiLot.Images != null && apiLot.Images.Any())
    {
      lot.Images = apiLot.Images.Select((imageUrl, index) => new InventAuctionLotImage
      {
        Id = Guid.NewGuid(),
        AuctionLotId = lot.Id, // Will be set properly when lot.Id is assigned
        ImageUrl = imageUrl,
        IsInterior = false, // You might want to determine this based on URL or other criteria
        SortOrder = index,
        StatusId = (uint)StatusEnum.Active,
        Added = DateTime.Now,
        Updated = DateTime.Now
      }).ToList();
    }

    // Set audit fields
    lot.Id = Guid.NewGuid();
    lot.StatusId = (uint)StatusEnum.Active;
    lot.Added = DateTime.Now;
    lot.Updated = DateTime.Now;

    // Update AuctionLotId in images after lot.Id is set
    if (lot.Images?.Any() == true)
    {
      foreach (var image in lot.Images)
      {
        image.AuctionLotId = lot.Id;
      }
    }

    return lot;
  }
}